<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Fri May  5 14:19:03 2017
 By 
Copyright (c) 2009-2011 by Accademia di Belle Arti di Urbino and students of MA course of Visual design. Some rights reserved.
</metadata>
<defs>
<font id="TitilliumWeb-Bold" horiz-adv-x="560" >
  <font-face 
    font-family="Titillium Web"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 8 0 0 0 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="500"
    cap-height="680"
    bbox="-224 -311 1059 1114"
    underline-thickness="30"
    underline-position="-110"
    unicode-range="U+0020-F6C3"
  />
<missing-glyph horiz-adv-x="235" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="235" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="220" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="280" 
d="M68 0v161h144v-161h-144zM81 250l-13 430h145l-14 -430h-118z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="411" 
d="M350 440h-118l-4 240h129zM178 440h-118l-5 240h129z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M544 149h-96v-149h-116v149h-104v-149h-116v149h-96v112h96v126h-96v112h96v161h116v-161h104v161h116v-161h96v-112h-96v-126h96v-112zM332 261v126h-104v-126h104z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M508 199q0 -104 -63 -157.5t-165 -53.5h-1l-15 -118q-74 3 -74 7l14 116q-74 9 -124 21l-20 4l13 104q79 -11 146 -15l22 174q-103 31 -146 75t-43 120q0 99 60 147.5t165 48.5h13l18 138h74l-18 -144q62 -6 114 -16l17 -4l-11 -106q-73 8 -135 12l-20 -161
q104 -33 141.5 -72.5t37.5 -119.5zM185 485q0 -24 15 -39t58 -31l17 139q-90 -4 -90 -69zM375 190q0 24 -13.5 39.5t-48.5 29.5l-19 -153q81 9 81 84z" />
    <glyph glyph-name="percent" unicode="%" 
d="M125 -8l235 707l73 -26l-235 -705zM134 672q120 0 120 -145.5t-120 -145.5t-120 145.5t120 145.5zM112 527q0 -36 4.5 -51t17.5 -15t17.5 15t4.5 51t-4.5 50.5t-17.5 14.5t-17.5 -14.5t-4.5 -50.5zM426 279q120 0 120 -145.5t-120 -145.5t-120 145.5t120 145.5zM404 134
q0 -36 4.5 -51t17.5 -15t17.5 15t4.5 51t-4.5 50.5t-17.5 14.5t-17.5 -14.5t-4.5 -50.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="701" 
d="M167 671.5q56 40.5 151.5 40.5t148.5 -41.5t53 -113.5t-30 -113t-107 -95l98 -98q6 15 12 58t7 70l130 -3q-15 -116 -51 -203l112 -98l-76 -87l-106 87q-35 -38 -89.5 -62.5t-115.5 -24.5q-146 0 -207.5 52.5t-61.5 155.5q0 81 34 127.5t108 75.5q-41 50 -53.5 81
t-12.5 81q0 70 56 110.5zM287 108q40 0 78 11t53 29l-173 173q-74 -36 -74 -114q0 -99 116 -99zM247 531q0 -40 45 -88l17 -18q41 30 58 53.5t17 56.5q0 63 -68.5 63t-68.5 -67z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="239" 
d="M60 440l-4 240h129l-7 -240h-118z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="308" 
d="M186 301q0 -88 24 -194.5t48 -168.5l23 -63h-127q-14 22 -37 74t-38.5 99t-28 116.5t-12.5 136.5t12 138t29 124q36 112 63 164l12 23h127q-34 -87 -64.5 -224t-30.5 -225z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="308" 
d="M270 301q0 -67 -12 -135.5t-29 -118.5q-36 -105 -63 -151l-12 -21h-127q34 77 64.5 207.5t30.5 218.5t-23.5 200.5t-47.5 180.5l-24 68h127q14 -25 37 -81.5t38.5 -106.5t28 -122t12.5 -139z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="425" 
d="M389 499h-123l38 -120l-73 -22l-41 121l-103 -79l-47 59l105 78l-102 73l46 63l101 -73l39 121l75 -25l-38 -122h123v-74z" />
    <glyph glyph-name="plus" unicode="+" 
d="M50 191v120h169v168h120v-168h171v-120h-171v-170h-120v170h-169z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="263" 
d="M20 -123l50 260h148l-90 -260h-108z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="408" 
d="M56 205v122h296v-122h-296z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="256" 
d="M56 0v166h144v-166h-144z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="466" 
d="M31 8l288 710l118 -42l-288 -710z" />
    <glyph glyph-name="zero" unicode="0" 
d="M280 672q127 0 191.5 -82t64.5 -263.5t-64 -260t-192 -78.5t-192 78.5t-64 260t64.5 263.5t191.5 82zM368 502.5q-26 49.5 -87.5 49.5t-88 -50t-26.5 -173t27 -172t88 -49t87 49t26 172.5t-26 173z" />
    <glyph glyph-name="one" unicode="1" 
d="M418 660v-660h-138v504l-143 -92l-63 103l218 145h126z" />
    <glyph glyph-name="two" unicode="2" 
d="M493 0h-428v117l141 143q75 77 104.5 118t29.5 87.5t-24.5 65.5t-69.5 19q-67 0 -146 -12l-24 -3l-7 107q98 30 203 30q210 0 210 -189q0 -74 -32 -128t-116 -131l-115 -104h274v-120z" />
    <glyph glyph-name="three" unicode="3" 
d="M62 636q92 36 205.5 36t165.5 -43.5t52 -139.5t-77 -147q48 -26 68.5 -54t20.5 -89q0 -107 -53.5 -159t-171.5 -52q-87 0 -183 26l-32 8l7 105q112 -19 188 -19q103 0 103 88q0 37 -26.5 59t-69.5 22h-133v114h133q33 0 58.5 28t25.5 62q0 71 -99 71q-69 0 -147 -14
l-26 -4z" />
    <glyph glyph-name="four" unicode="4" 
d="M320 0v109h-280v105l163 446h152l-177 -431h142v185h138v-185h63v-120h-63v-109h-138z" />
    <glyph glyph-name="five" unicode="5" 
d="M490 660v-120h-295l-17 -143q62 24 118 24q214 0 214 -199q0 -112 -59 -173t-167 -61q-46 0 -104.5 9t-93.5 19l-36 9l15 102q121 -19 202 -19q49 0 75 26t26 74.5t-22 70.5t-62 22q-67 0 -118 -13l-15 -4l-82 19l18 357h403z" />
    <glyph glyph-name="six" unicode="6" 
d="M496 537q-102 15 -177 15q-142 0 -142 -154l21 6q66 19 105 19q113 0 170 -52t57 -161.5t-64 -165.5t-185.5 -56t-183.5 88.5t-62 260.5t71 253.5t204 81.5q78 0 167 -22l31 -7zM291 303q-47 0 -98 -17l-17 -5q0 -173 112 -173q48 0 74 26.5t26 74.5q0 94 -97 94z" />
    <glyph glyph-name="seven" unicode="7" 
d="M69 537v123h422v-156l-235 -516l-128 36l225 481v32h-284z" />
    <glyph glyph-name="eight" unicode="8" 
d="M278 672q109 0 176 -48t67 -133q0 -58 -14 -87t-58 -64q44 -37 63 -70t19 -91q0 -98 -70 -144.5t-183 -46.5q-250 0 -250 180q0 64 19.5 99.5t63.5 72.5q-42 34 -57 66.5t-15 85.5q0 85 65 132.5t174 47.5zM176 200q0 -88 103.5 -88t103.5 88q0 52 -59 75h-88
q-60 -23 -60 -75zM280 548q-45 0 -69 -20t-24 -61t49 -75h88q49 34 49 78q0 78 -93 78z" />
    <glyph glyph-name="nine" unicode="9" 
d="M248 108q133 0 133 162l-21 -7q-69 -23 -105 -23q-109 0 -168 53t-59 157.5t65 163t184.5 58.5t182.5 -88.5t63 -263t-70 -253.5t-205 -79q-78 0 -167 22l-31 7l12 106q102 -15 186 -15zM267 360q46 0 99 18l16 5q0 169 -112 169q-48 0 -74 -26.5t-26 -74.5q0 -91 97 -91
z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="256" 
d="M56 289v166h144v-166h-144zM56 0v166h144v-166h-144z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="279" 
d="M77 137h148l-90 -260h-108zM69 289v166h144v-166h-144z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M472 353l-261 -99l261 -108v-136l-406 186v110l406 183v-136z" />
    <glyph glyph-name="equal" unicode="=" 
d="M58 295v121h444v-121h-444zM58 87v121h444v-121h-444z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M349 254l-261 99v136l406 -183v-110l-406 -186v136z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="437" 
d="M356.5 653q48.5 -39 48.5 -117t-15.5 -113.5t-65.5 -73t-63 -57.5t-13 -44v-31h-107q-31 34 -31 91q0 36 63.5 87t78 70.5t14.5 47.5q0 59 -85 59q-60 0 -123 -12l-21 -4l-7 101q86 35 182 35t144.5 -39zM119 0v161h144v-161h-144z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="973" 
d="M944 305v-8q0 -154 -48.5 -225.5t-144.5 -71.5q-71 0 -116 36q-10 8 -17 20q-80 -56 -148 -56q-94 0 -140.5 58.5t-46.5 189t43.5 191.5t147.5 61q35 0 76 -18l15 -7v13h134v-179q0 -138 5.5 -158t12 -28t13.5 -9.5t19 -1.5q32 0 46.5 39t14.5 146v9q0 160 -73.5 232.5
t-234 72.5t-245.5 -92t-85 -267.5t76 -259t255 -83.5l143 9l5 -117q-90 -10 -148 -10q-115 0 -198 23t-145 76q-122 107 -122 371q0 231 125 349.5t342 118.5t328 -108.5t111 -315.5zM481 118q38 0 90 25q-6 36 -6 106v123q-42 10 -61 10q-52 0 -68.5 -29t-16.5 -102
q0 -133 62 -133z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="609" 
d="M74 680h267q106 0 159 -42.5t53 -137.5q0 -57 -17 -90.5t-58 -59.5q45 -19 66.5 -55t21.5 -100q0 -99 -58 -147t-163 -48h-271v680zM338 284h-126v-166h126q45 0 66.5 18t21.5 66q0 82 -88 82zM334 562h-122v-162h123q78 0 78 81t-79 81z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="543" 
d="M505 11q-106 -23 -191 -23t-136 21t-80 67t-40 108t-11 156q0 197 55.5 274.5t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5t110 -45.5t175.5 14z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="638" 
d="M303 0h-229v680h229q88 0 145 -18.5t88.5 -61t43.5 -100t12 -149.5t-11 -152.5t-41.5 -108t-88.5 -69t-148 -21.5zM448 270q2 32 2 88.5t-4 90.5t-19 62t-44.5 38.5t-79.5 10.5h-91v-440h91q75 0 109 38q29 31 36 112z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="531" 
d="M74 0v680h434v-120h-296v-200h242v-120h-242v-240h-138z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="611" 
d="M357 244v120h198v-353q-143 -23 -232 -23q-159 0 -218.5 85t-59.5 270t62 267t211 82q93 0 201 -21l36 -7l-4 -107q-120 13 -196.5 13t-107.5 -18t-45.5 -66.5t-14.5 -166t29 -164.5t120 -47l83 4v132h-62z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="677" 
d="M465 0v282h-253v-282h-138v680h138v-278h253v278h138v-680h-138z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="302" 
d="M19 -70v120q40 0 58 15t18 56v559h137l1 -565q0 -109 -48.5 -147t-165.5 -38z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="593" 
d="M212 0h-138v680h138v-299l92 10l112 289h157l-148 -341l152 -339h-159l-114 271l-92 -10v-261z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="472" 
d="M461 0h-387v680h138v-558h249v-122z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="858" 
d="M74 0v680h235l120 -496l120 496h235v-680h-138v526h-15l-133 -496h-138l-133 496h-15v-526h-138z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="693" 
d="M74 0v680h233l164 -560h10v560h138v-680h-227l-170 560h-10v-560h-138z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="589" 
d="M324 198h-112v-198h-138v680h250q242 0 242 -236q0 -119 -61.5 -182.5t-180.5 -63.5zM212 316h111q103 0 103 128q0 63 -25 90.5t-78 27.5h-111v-246z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="652" 
d="M326 -12q-156 0 -219 84t-63 264.5t63.5 268t218.5 87.5t218.5 -87.5t63.5 -268.5q0 -222 -105 -299l83 -134l-126 -59l-90 147q-10 -3 -44 -3zM326 108q81 0 110.5 52t29.5 176t-30 180t-110 56t-110 -56t-30 -180t29.5 -176t110.5 -52z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="620" 
d="M212 228v-228h-138v680h264q240 0 240 -224q0 -133 -100 -196l97 -260h-151l-79 228h-133zM413 376q24 30 24 79t-25.5 78t-73.5 29h-126v-216h128q49 0 73 30z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="544" 
d="M283 572q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -124.5q0 -107 -65 -162t-170 -55q-78 0 -191 24l-36 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152t170 50q74 0 188 -20l36 -7
l-11 -109q-141 16 -199 16z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="526" 
d="M13 558v122h500v-122h-180v-558h-138v558h-182z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="603" 
d="M442 680h145l-158 -680h-255l-158 680h145l118 -560h45z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="930" 
d="M19 680h145l84 -562h15l122 562h160l122 -562h15l84 562h145l-130 -680h-209l-107 517l-107 -517h-209z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="569" 
d="M162 680l126 -250l129 250h143l-196 -347l196 -333h-153l-126 235l-129 -235h-143l196 326l-196 354h153z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="557" 
d="M348 0h-138v275l-210 405h153l125 -271l125 271h153l-208 -405v-275z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="532" 
d="M41 560v120h450v-120l-284 -422v-18h284v-120h-450v119l284 423v18h-284z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="355" 
d="M320 749v-120h-115v-632h115v-120h-252v872h252z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="498" 
d="M466 13l-116 -49l-318 700l114 52z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="355" 
d="M35 629v120h252v-872h-252v120h115v632h-115z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M383 313l-109 217l-113 -217h-140l194 347h114l194 -347h-140z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="620" 
d="M96 -73h428v-114h-428v114z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="268" 
d="M23 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="544" 
d="M304 512q105 0 153.5 -57.5t48.5 -205.5t-56 -204.5t-193 -56.5q-43 0 -157 10l-38 4v698h134v-215q64 27 108 27zM257 108q67 0 90 32.5t23 116.5q0 135 -84 135q-40 0 -76 -10l-14 -3v-267q46 -4 61 -4z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="446" 
d="M249 512q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5t92.5 -30.5l116 8l4 -107q-107 -21 -163 -21q-111 0 -159.5 62.5t-48.5 200.5t50 199.5t161 61.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="549" 
d="M487 700v-700h-133v21q-70 -33 -121 -33q-109 0 -152 63t-43 199.5t51.5 199t155.5 62.5q32 0 89 -10l19 -4v202h134zM339 120l14 3v263q-55 10 -98 10q-81 0 -81 -144q0 -78 18 -111t59.5 -33t87.5 12z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="357" 
d="M220 386v-386h-134v386h-54v114h54v22q0 108 32 149t114 41q36 0 93 -10l21 -3l-2 -109q-44 2 -74 2t-40 -16t-10 -55v-21h121v-114h-121z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="534" 
d="M449 -189q-66 -41 -178.5 -41t-173 30t-60.5 109q0 63 72 119q-37 25 -37 76q0 20 30 66l9 14q-68 49 -68 144.5t57.5 138.5t152.5 43q43 0 85 -10l16 -3l161 5v-107l-75 6q22 -34 22 -68q0 -100 -51 -138t-160 -38q-23 0 -41 4q-10 -26 -10 -42.5t17 -22.5t80 -7
q126 -1 172 -33.5t46 -118t-66 -126.5zM169 -77q0 -43 105.5 -43t105.5 50q0 27 -18 34.5t-76 8.5l-90 7q-27 -27 -27 -57zM253.5 259q76.5 0 76.5 73.5t-76.5 73.5t-76.5 -73.5t76.5 -73.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="553" 
d="M196 0h-134v700h134v-224q69 36 124 36q100 0 138 -59t38 -182v-271h-134v268q0 63 -15 93.5t-60 30.5q-39 0 -78 -12l-13 -4v-376z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="258" 
d="M62 0v500h134v-500h-134zM62 564v136h134v-136h-134z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="259" 
d="M63 24v476h134v-477q0 -102 -35.5 -150.5t-142.5 -102.5l-53 99q45 30 63 47t26 40.5t8 67.5zM63 564v136h134v-136h-134z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="515" 
d="M196 0h-134v700h134v-398l51 9l99 189h150l-130 -237l137 -263h-151l-101 195l-55 -9v-186z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="270" 
d="M68 0v700h134v-700h-134z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="839" 
d="M196 0h-134v500h133v-28q69 40 118 40q81 0 125 -47q92 47 168 47q99 0 137.5 -57.5t38.5 -183.5v-271h-134v267q0 64 -14 94.5t-56 30.5q-32 0 -78 -14l-15 -5q4 -75 4 -114v-259h-134v257q0 74 -13 104.5t-57 30.5q-39 0 -77 -14l-12 -4v-374z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="553" 
d="M196 0h-134v500h133v-28q68 40 125 40q100 0 138 -59t38 -182v-271h-134v267q0 64 -15 94.5t-60 30.5q-41 0 -79 -14l-12 -4v-374z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="544" 
d="M62 -210v710h133v-27q65 39 114 39q101 0 149 -60.5t48 -205.5t-53 -201.5t-174 -56.5q-33 0 -71 6l-12 2v-206h-134zM283 392q-37 0 -75 -15l-12 -5v-262q45 -6 73 -6q58 0 79.5 34t21.5 116q0 138 -87 138z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="543" 
d="M239 -12q-105 0 -153 57.5t-48 206t55.5 204.5t192.5 56q61 0 159 -10l36 -3v-709h-134v225q-64 -27 -108 -27zM286 396q-67 0 -90.5 -34.5t-23.5 -118.5q0 -135 85 -135q40 0 77 10l13 3v271q-37 4 -61 4z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="372" 
d="M62 0v500h133v-53q84 50 160 65v-135q-81 -17 -139 -35l-20 -7v-335h-134z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="472" 
d="M421 375q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -162 -201 -162q-66 0 -160 18l-32 6l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19l33 -6z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="364" 
d="M339 386h-123v-205q0 -30 1.5 -42.5t11 -21.5t29.5 -9l76 2l6 -107q-67 -15 -102 -15q-90 0 -123 40.5t-33 149.5v208h-59v114h59v139h134v-139h123v-114z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="553" 
d="M357 500h134v-500h-133v28q-72 -40 -125 -40q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t58 -27q44 0 83 14l13 4v374z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="498" 
d="M15 500h140l80 -386h28l84 386h136l-120 -500h-228z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="772" 
d="M24 500h132l61 -386h24l76 376h138l76 -376h24l61 386h132l-95 -500h-210l-57 306l-57 -306h-210z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="474" 
d="M14 500h143l80 -155l81 155h143l-142 -245l142 -255h-143l-81 153l-80 -153h-143l137 251z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="500" 
d="M16 500h132l91 -386h23l91 386h132l-176 -710h-131l55 210h-94z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="454" 
d="M41 380v120h371v-120l-210 -260h210v-120h-371v120l210 260h-210z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="361" 
d="M251 576l7 -128q0 -63 -20.5 -90t-91.5 -44q70 -17 91.5 -47.5t21.5 -94.5l-7 -117q0 -34 14.5 -53t56.5 -22v-114q-111 4 -156.5 43t-45.5 129l7 121q0 73 -112 102v104q112 26 112 94l-7 127q0 95 45.5 133t158.5 42v-114q-43 -4 -58.5 -21t-15.5 -50z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="268" 
d="M67 -210v910h134v-910h-134z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="361" 
d="M109 55l-7 117q0 64 21.5 94.5t91.5 47.5q-71 17 -91.5 44t-20.5 90l7 128q0 33 -15.5 50t-58.5 21v114q113 -4 158.5 -42t45.5 -133l-7 -127q0 -68 112 -94v-104q-112 -29 -112 -102l7 -121q0 -90 -45.5 -129t-156.5 -43v114q42 3 56.5 22t14.5 53z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M383 170q-28 0 -107 22t-98 22t-46.5 -10t-43.5 -20l-17 -9l-10 107q63 52 123 52q28 0 104 -22t96 -22q37 0 88 28l17 10l8 -107q-19 -19 -52.5 -35t-61.5 -16z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="220" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="264" 
d="M204 500v-161h-144v161h144zM191 250l13 -430h-145l14 430h118z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M270 -75v104q-182 13 -182 219.5t182 221.5v101h110v-105l79 -13l-4 -100q-79 3 -129.5 3t-76 -24.5t-25.5 -81.5t26 -82t89 -25l116 4l4 -100q-39 -10 -79 -13v-109h-110z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M333 551q-52 0 -52 -65v-53h153v-114h-153v-205h124l69 16l21 -112l-81 -18h-339v114h74v205h-64v114h64v59q0 104 40 141.5t126 37.5q67 0 131 -18l22 -6l-4 -104q-68 8 -131 8z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M280 55q-45 0 -85 20l-73 -73l-92 92l72 72q-19 41 -19 85.5t19 85.5l-72 73l92 92l72 -73q40 20 85.5 20t85.5 -20l73 73l92 -92l-73 -73q20 -40 20 -85.5t-20 -85.5l73 -72l-92 -92l-73 73q-40 -20 -85 -20zM280 166q35 0 60.5 25.5t25.5 60.5t-25.5 60.5t-60.5 25.5
t-60.5 -25.5t-25.5 -60.5t25.5 -60.5t60.5 -25.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M58 291v112h92l-145 257h153l123 -211l122 211h153l-143 -257h89v-112h-151v-45h151v-112h-151v-134h-138v134h-155v112h155v45h-155z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="272" 
d="M69 700h134v-372h-134v372zM69 170h134v-380h-134v380z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="517" 
d="M444 506q-113 16 -166 16t-72 -14t-19 -51q0 -22 27 -33.5t113 -32.5t117 -50t31 -98.5t-50 -140.5q20 -19 29 -41t9 -68q0 -97 -51.5 -140t-165.5 -43q-56 0 -155 18l-31 5l13 108q108 -15 161.5 -15t77 13.5t23.5 42t-25 40.5t-115 34.5t-124 54t-34 102.5
q0 35 20 74.5t44 59.5q-44 32 -44 108q0 183 217 183q64 0 148 -18l28 -6zM189 302q-17 -26 -17 -59.5t11.5 -44.5t51 -22t86.5 -27q18 23 18 73q0 25 -16 37.5t-44.5 20t-53.5 13.5t-36 9z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="268" 
d="M-30 596v132h128v-132h-128zM186 596v132h128v-132h-128z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="643" 
d="M124.5 232q-78.5 82 -78.5 203.5t78 202t199.5 80.5t197.5 -82.5t76 -203.5t-76 -201.5t-197 -80.5t-199.5 82zM108 434q0 -93 61 -158.5t152.5 -65.5t152.5 65t61 158t-61.5 159t-152 66t-152 -65.5t-61.5 -158.5zM414 287q-28 -18 -90.5 -18t-92 36t-29.5 129.5
t30.5 128.5t101.5 35q39 0 70 -12l10 -3l-7 -86q-35 6 -61 6t-33.5 -14t-7.5 -50q0 -75 35 -75l67 5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="416" 
d="M351 544v-141q8 -6 18 -8l-2 -82q-68 0 -91.5 7.5t-35.5 25.5q-42 -32 -92 -32t-78 29.5t-28 80.5q0 95 127 101l67 3v22q0 15 -44 15l-131 -7l-3 75q70 26 144 26t111.5 -24.5t37.5 -90.5zM188 402q18 0 48 16v40l-47 -3q-32 -3 -32 -27q0 -26 31 -26z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="601" 
d="M263 319l-109 -75l109 -86v-135l-223 171v96l223 164v-135zM546 319l-109 -75l109 -86v-135l-223 171v96l223 164v-135z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M57 358h436v-254h-120v134h-316v120z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="643" 
d="M124.5 231.5q-78.5 81.5 -78.5 203.5t78 202.5t199 80.5t197.5 -82.5t76.5 -203.5t-75.5 -201.5t-197 -80.5t-200 81.5zM108 434q0 -92 62 -158t152.5 -66t151.5 65.5t61 158t-61.5 158.5t-152 66t-152 -66t-61.5 -158zM297 369v-93h-96v315h136q52 0 83 -25.5t31 -72
t-8 -68t-30 -39.5l41 -110h-100l-28 93h-29zM296 519v-79h28q34 0 34 39.5t-35 39.5h-27z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="268" 
d="M-5 597v100h287v-100h-287z" />
    <glyph glyph-name="degree" unicode="&#xb0;" 
d="M130 542q0 67 41.5 108.5t108.5 41.5t108.5 -41.5t41.5 -108.5t-41.5 -108t-108.5 -41t-108.5 41t-41.5 108zM205 542q0 -34 20.5 -55t54.5 -21t55.5 21t21.5 55t-21.5 55.5t-55.5 21.5t-54.5 -21.5t-20.5 -55.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M50 270v120h169v108h120v-108h171v-120h-171v-100h-120v100h-169zM50 139h460v-120h-460v120z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="280" 
d="M252 478h-231v90l73 59q46 37 46 57q0 15 -37 15l-76 -6l-4 99q73 9 126 9t78 -23t25 -66.5t-13 -66t-43 -44.5l-38 -28h94v-95z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="280" 
d="M132 801q121 0 121 -89q0 -53 -29 -70q35 -15 35 -70q0 -105 -118 -105l-121 8l6 93q61 -6 92 -6t31 16q0 14 -26 14h-66v85h64q20 0 20 14.5t-27 14.5l-84 -5l-6 91q68 9 108 9z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="268" 
d="M5 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="mu" unicode="&#xb5;" 
d="M363 500h134v-500h-133v28q-72 -40 -125 -40q-23 0 -42 3v-201h-134v710h134v-289q1 -58 14 -80.5t56 -22.5q44 0 83 14l13 4v374z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="599" 
d="M397 0v566h-62v-566h-114v286h-7q-84 0 -135 54.5t-51 141.5t52 142.5t136 55.5h331v-114h-36v-566h-114z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="256" 
d="M56 167v166h144v-166h-144z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="264" 
d="M204.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-44 0 -79 8l-14 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v106h53v-38q64 -1 93.5 -19z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="280" 
d="M212 790v-312h-106v195l-47 -32l-49 74l106 75h96z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="420" 
d="M211 659q169 0 169 -172.5t-169 -172.5q-171 0 -171 172.5t171 172.5zM211 419q29 0 38.5 16t9.5 53t-9.5 52t-38.5 15t-40 -15.5t-11 -51.5t11 -52.5t40 -16.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="603" 
d="M447 244l-109 75v135l223 -164v-96l-223 -171v135zM164 244l-109 75v135l223 -164v-96l-223 -171v135z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="548" 
d="M220 790v-312h-106v195l-47 -32l-49 74l106 75h96zM29 29l393 632l53 -35l-393 -633zM409 -100v35h-132v88l39 189h118l-52 -182h27l14 90h91v-90h11v-95h-11v-35h-105z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="542" 
d="M220 790v-312h-106v195l-47 -32l-49 74l106 75h96zM29 29l393 632l53 -35l-393 -633zM512 -100h-231v90l73 59q46 37 46 57q0 15 -37 15l-76 -6l-4 99q73 9 126 9t78 -23t25 -66.5t-13 -66t-43 -44.5l-38 -28h94v-95z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="561" 
d="M148 801q121 0 121 -89q0 -53 -29 -70q35 -15 35 -70q0 -105 -118 -105l-121 8l6 93q61 -6 92 -6t31 16q0 14 -26 14h-66v85h64q20 0 20 14.5t-27 14.5l-84 -5l-6 91q68 9 108 9zM43 29l393 632l53 -35l-393 -633zM423 -100v35h-132v88l39 189h118l-52 -182h27l14 90h91
v-90h11v-95h-11v-35h-105z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="433" 
d="M77.5 -153q-48.5 39 -48.5 117t15.5 113.5t65.5 73t63 57.5t13 44v31h107q31 -34 31 -91q0 -36 -63.5 -87t-78 -70.5t-14.5 -47.5q0 -59 85 -59q60 0 123 12l21 4l7 -101q-86 -35 -182 -35t-144.5 39zM315 500v-161h-144v161h144z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM183 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM162 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM107 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM377 758q-26 0 -89.5 23t-76.5 23q-25 0 -67 -32l-14 -10l-29 96q20 24 51.5 44t61 20t90.5 -23t72 -23q23 0 66 32l15 10l29 -97q-21 -24 -52 -43.5t-57 -19.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM129 764v132h128v-132h-128zM343 764v132h128v-132h-128z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="602" 
d="M451 721q0 -31 -15 -59l149 -662h-138l-27 123h-238l-27 -123h-138l149 662q-15 26 -15 59q0 59 43 92.5t107 33.5t107 -33.5t43 -92.5zM277 566l-69 -323h186l-69 323h-48zM258 751q-16 -11 -16 -29.5t14 -29.5t39 -12h6q27 0 43 11t16 30t-16 30t-43 11t-43 -11z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="871" 
d="M392 0v116h-213l-29 -116h-138l184 690h634v-132h-302v-143h242v-131h-242v-152h302v-132h-438zM293 558l-80 -309h179l1 309h-100z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="543" 
d="M408.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-43 0 -79 8l-14 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v95q-124 12 -169.5 94t-45.5 267.5t55.5 263t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5
t110 -45.5t175.5 14l3 -113q-94 -21 -190 -23v-25q64 -1 93.5 -19z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM180 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM154 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM105 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM123 764v132h128v-132h-128zM337 764v132h128v-132h-128z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM17 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM7 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM-50 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM-28 764v132h128v-132h-128zM186 764v132h128v-132h-128z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="642" 
d="M25 277v132h54v281h227q88 0 145 -19t88.5 -62.5t43.5 -102t12 -151t-11 -153.5t-41.5 -109.5t-88.5 -70.5t-148 -22h-227v277h-54zM453 354q0 43 -1.5 68t-6.5 50t-15 39.5t-26 26.5q-31 21 -98 21h-91v-150h130v-132h-130v-146h91q75 0 110 38q37 41 37 185z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="693" 
d="M74 0v680h233l164 -560h10v560h138v-680h-227l-170 560h-10v-560h-138zM425 758q-26 0 -89.5 23t-76.5 23q-25 0 -67 -32l-14 -10l-29 96q20 24 51.5 44t61 20t90.5 -23t72 -23q23 0 66 32l15 10l29 -97q-21 -24 -52 -43.5t-57 -19.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM204 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM167 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM135 765l134 149h117l134 -149h-142l-49 56
l-52 -56h-142z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM409 758q-26 0 -89.5 23t-76.5 23
q-25 0 -67 -32l-14 -10l-29 96q20 24 51.5 44t61 20t90.5 -23t72 -23q23 0 66 32l15 10l29 -97q-21 -24 -52 -43.5t-57 -19.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM155 764v132h128v-132h-128zM369 764v132h128
v-132h-128z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M142 473l138 -141l140 143l84 -85l-143 -140l143 -139l-85 -85l-139 142l-139 -141l-85 84l142 139l-141 138z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="652" 
d="M326 -12q-49 0 -96 10l-58 -125l-98 49l58 124q-88 78 -88 290q0 181 63.5 268.5t218.5 87.5q58 0 101 -12l61 130l100 -43l-65 -140q85 -82 85 -291q0 -179 -63.5 -263.5t-218.5 -84.5zM216 516q-30 -56 -30 -164.5t15 -157.5l173 371q-24 7 -48 7q-80 0 -110 -56z
M326 108q81 0 110.5 52.5t29.5 158.5t-14 156l-169 -363q17 -4 43 -4z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM216 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM175 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM135 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM156 764v132h128v-132h-128zM370 764v132h128v-132h-128z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="557" 
d="M348 0h-138v275l-210 405h153l125 -271l125 271h153l-208 -405v-275zM133 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="596" 
d="M323 233q51 0 78 31.5t27 88.5q0 107 -105 107h-111v-227h111zM324 99h-112v-99h-138v690h138v-98h112q244 0 244 -239q0 -121 -62.5 -187.5t-181.5 -66.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="611" 
d="M196 0h-134v522q0 102 53.5 146t173.5 44t177 -37t57 -120q0 -54 -21.5 -79t-65.5 -44.5t-55 -28.5t-11 -23t16 -26t82.5 -42.5t93 -60t26.5 -87.5q0 -101 -45.5 -138.5t-156.5 -37.5q-54 0 -130 18l-24 5l4 107q97 -12 134 -12q80 0 80 38q0 23 -15 35t-81 40t-94 61.5
t-28 84.5t18 79t62 46t58.5 29t14.5 32t-20 32t-73.5 11t-74.5 -17.5t-21 -57.5v-519z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM143 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM96 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM83 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM340 602q-28 -17 -50.5 -17t-68.5 18t-59 18q-29 0 -66 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM65 596v132h128v-132h-128zM281 596v132h128v-132h-128z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM181.5 554.5q-33.5 33.5 -33.5 83t33.5 83.5t83 34t83.5 -34t34 -83.5t-34 -83t-83.5 -33.5t-83 33.5zM220 638q0 -20 13 -33t33 -13t32.5 13t12.5 33t-12.5 32.5t-32.5 12.5t-33 -12.5t-13 -32.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="784" 
d="M469 130q23 -19 68 -19q68 0 166 7l28 2l2 -104q-115 -28 -212.5 -28t-148.5 48l-23 -11q-84 -37 -164 -37t-118 43.5t-38 122t43.5 113t130.5 39.5l108 7v25q0 20 -14 32.5t-41 12.5q-72 0 -157 -8l-32 -3l-4 117q133 22 212.5 22t122.5 -37q52 37 133 37
q218 0 218 -225l-10 -94h-294q1 -43 24 -62zM311 118l-1 93l-90 -4q-56 -3 -56 -51q0 -53 41 -53q40 0 89 11zM445 295h172q0 53 -19.5 75t-67 22t-66.5 -23t-19 -74z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="446" 
d="M348.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-43 0 -79 8l-14 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v96q-87 11 -125.5 74t-38.5 193.5t50 192t161 61.5q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5
t92.5 -30.5l116 8l4 -107q-97 -19 -154 -21v-25q64 -1 93.5 -19z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM150 753l252 -102l-30 -87l-262 74z
" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM105 651l252 102l40 -115l-262 -74z
" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM89 580l126 151h84l128 -151h-123
l-46 67l-46 -67h-123z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM84 596v132h128v-132h-128zM300 596
v132h128v-132h-128z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="258" 
d="M62 0v500h134v-500h-134zM-19 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="258" 
d="M31 651l252 102l40 -115l-262 -74zM62 500h134v-500h-134v500z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="258" 
d="M62 500h134v-500h-134v500zM-45 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="258" 
d="M62 500h134v-500h-134v500zM-48 596v132h128v-132h-128zM168 596v132h128v-132h-128z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="567" 
d="M405 625q123 -88 123 -274.5t-60 -273t-195 -86.5q-235 0 -235 225q0 104 54.5 163t163.5 59q62 0 113 -18l17 -6q-5 47 -25 80.5t-64 58.5l-129 -86l-54 76l76 51q-46 12 -108 24l18 94q121 -9 209 -39l96 64l54 -76zM264 320q-41 0 -62.5 -30.5t-21.5 -73.5
q0 -105 93 -105q56 0 84.5 44t28.5 143q-50 22 -122 22z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="553" 
d="M62 0v500h133v-28l19 10q18 10 49.5 20t56.5 10q100 0 138 -59t38 -182v-271h-134v267q0 64 -15 94.5t-61 30.5t-90 -18v-374h-134zM397 602q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9
l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM163 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM116 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM91 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM377 602q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75
q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM93 596v132h128v-132h-128zM309 596v132h128v-132h-128z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M213 352v132h132v-132h-132zM50 191v120h460v-120h-460zM213 17v132h132v-132h-132z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="532" 
d="M266 512q28 0 56 -5l40 100l81 -30l-41 -98q94 -59 94 -228q0 -263 -230 -263q-34 0 -63 6l-41 -102l-80 30l43 103q-89 60 -89 226q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 107t-14 105l-100 -247q6 -1 20 -1zM279 397l-13 1q-53 0 -73.5 -35t-20.5 -105
t12 -102z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="553" 
d="M357 500h134v-500h-133v28q-72 -40 -125 -40q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t58 -27q44 0 83 14l13 4v374zM138 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM130 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM82 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM106 596v132h128v-132h-128zM322 596v132h128v-132h-128z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="500" 
d="M16 500h132l91 -386h23l91 386h132l-176 -710h-131l55 210h-94zM122 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="544" 
d="M269 104q58 0 79.5 34t21.5 117q0 137 -84 137q-40 0 -76 -10l-14 -3v-269q45 -6 73 -6zM304 512q105 0 153.5 -57.5t48.5 -205.5t-53 -204.5t-174 -56.5q-33 0 -71 6l-12 2v-206h-134v910h134v-215q64 27 108 27z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="500" 
d="M16 500h132l91 -386h23l91 386h132l-176 -710h-131l55 210h-94zM79 596v132h128v-132h-128zM295 596v132h128v-132h-128z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM124 782v98h347v-98h-347z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM107 597v100h287v-100h-287z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="602" 
d="M17 0l153 680h262l153 -680h-138l-27 123h-238l-27 -123h-138zM277 566l-69 -323h186l-69 323h-48zM253 869q17 -16 44.5 -16t45 16t20.5 41h121q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="512" 
d="M445 154q0 -29 6 -38.5t26 -13.5l-4 -114q-60 0 -87 7.5t-55 26.5l-23 -8q-69 -26 -130 -26q-73 0 -111 43.5t-38 122t42.5 112.5t131.5 40l108 7v25q0 20 -14 32.5t-36 12.5q-74 0 -194 -8l-4 114l35 5q107 17 171 17q88 0 132 -39.5t44 -132.5v-185zM205 103
q41 0 106 15v93l-91 -4q-56 -3 -56 -51q0 -53 41 -53zM198 748q7 -44 55.5 -44t54.5 44h139q-8 -75 -56.5 -119t-137.5 -44t-137.5 44t-56.5 119h139z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="602" 
d="M567 0q-56 -47 -56 -80.5t32 -33.5l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 68 66 120h-5l-27 123h-238l-27 -123h-138l153 680h262l153 -680h-18zM277 566l-69 -323h186l-69 323h-48z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="510" 
d="M474 -12l-38 1q-44 -41 -44 -70q0 -33 32 -33l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 66t20.5 72.5t40.5 47.5l20 15l3 -1l-20 13q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29
t139 -40t42.5 -128v-208q2 -24 8 -33.5t25 -13.5zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4v107z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="543" 
d="M505 11q-106 -23 -191 -23t-136 21t-80 67t-40 108t-11 156q0 197 55.5 274.5t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5t110 -45.5t175.5 14zM147 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="446" 
d="M249 512q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5t92.5 -30.5l116 8l4 -107q-107 -21 -163 -21q-111 0 -159.5 62.5t-48.5 200.5t50 199.5t161 61.5zM97 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="543" 
d="M505 11q-106 -23 -191 -23t-136 21t-80 67t-40 108t-11 156q0 197 55.5 274.5t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5t110 -45.5t175.5 14zM107 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="446" 
d="M249 512q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5t92.5 -30.5l116 8l4 -107q-107 -21 -163 -21q-111 0 -159.5 62.5t-48.5 200.5t50 199.5t161 61.5zM54 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="543" 
d="M505 11q-106 -23 -191 -23t-136 21t-80 67t-40 108t-11 156q0 197 55.5 274.5t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5t110 -45.5t175.5 14zM219 760v135h134v-135h-134z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="446" 
d="M249 512q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5t92.5 -30.5l116 8l4 -107q-107 -21 -163 -21q-111 0 -159.5 62.5t-48.5 200.5t50 199.5t161 61.5zM189 556v135h134v-135h-134z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="543" 
d="M505 11q-106 -23 -191 -23t-136 21t-80 67t-40 108t-11 156q0 197 55.5 274.5t204.5 77.5q86 0 199 -27l-4 -110q-99 15 -164.5 15t-93.5 -17.5t-41.5 -66.5t-13.5 -167t27.5 -163.5t110 -45.5t175.5 14zM250 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="446" 
d="M249 512q52 0 133 -16l27 -6l-4 -106q-79 8 -117 8q-69 0 -91.5 -29.5t-22.5 -111.5t22.5 -112.5t92.5 -30.5l116 8l4 -107q-107 -21 -163 -21q-111 0 -159.5 62.5t-48.5 200.5t50 199.5t161 61.5zM199 580l-126 151h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="638" 
d="M303 0h-229v680h229q88 0 145 -18.5t88.5 -61t43.5 -100t12 -149.5t-11 -152.5t-41.5 -108t-88.5 -69t-148 -21.5zM448 270q2 32 2 88.5t-4 90.5t-19 62t-44.5 38.5t-79.5 10.5h-91v-440h91q75 0 109 38q29 31 36 112zM246 765l-134 149h142l52 -56l49 56h142l-134 -149
h-117z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="664" 
d="M590 678h130l-55 -234h-121zM487 700v-700h-133v21q-70 -33 -121 -33q-109 0 -152 63t-43 199.5t51.5 199t155.5 62.5q32 0 89 -10l19 -4v202h134zM339 120l14 3v263q-55 10 -98 10q-81 0 -81 -144q0 -78 18 -111t59.5 -33t87.5 12z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="642" 
d="M25 277v132h54v281h227q88 0 145 -19t88.5 -62.5t43.5 -102t12 -151t-11 -153.5t-41.5 -109.5t-88.5 -70.5t-148 -22h-227v277h-54zM453 354q0 43 -1.5 68t-6.5 50t-15 39.5t-26 26.5q-31 21 -98 21h-91v-150h130v-132h-130v-146h91q75 0 110 38q37 41 37 185z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="549" 
d="M129 622v116h327v-38h31v-700h-133v21q-70 -33 -121 -33q-109 0 -152 63t-43 199.5t51.5 199t155.5 62.5q32 0 89 -10l19 -4v124h-224zM339 120l14 3v263q-55 10 -98 10q-81 0 -81 -144q0 -78 18 -111t59.5 -33t87.5 12z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM116 782v98h347v-98h-347z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM113 597v100h287v-100h-287z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM256 869q17 -16 44.5 -16t45 16t20.5 41h121q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM258 664q56 0 63 66h101
q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM233 767v135h134v-135h-134z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM190 556v135h134v-135h-134z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-28q-56 -47 -56 -80.5t32 -33.5l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 68 66 120h-297z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="511" 
d="M407 1q-58 -47 -58 -81t32 -34l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 59 52 109q-8 -1 -24 -1q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5t67.5 -20.5q93 0 166 6l28 3l2 -99q-30 -7 -67 -14zM344 295
q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="554" 
d="M74 0v680h440v-120h-302v-159h242v-118h-242v-163h302v-120h-440zM221 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="510" 
d="M196.5 126.5q23.5 -20.5 67.5 -20.5q93 0 166 6l28 3l2 -99q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 269 221 269q218 0 218 -226l-10 -93h-294q1 -46 24.5 -66.5zM344 295q0 60 -19 83.5t-66.5 23.5t-67 -24.5t-20.5 -82.5h173zM211 580l-126 151h123l46 -67l46 67
h123l-128 -151h-84z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="611" 
d="M357 244v120h198v-353q-143 -23 -232 -23q-159 0 -218.5 85t-59.5 270t62 267t211 82q93 0 201 -21l36 -7l-4 -107q-120 13 -196.5 13t-107.5 -18t-45.5 -66.5t-14.5 -166t29 -164.5t120 -47l83 4v132h-62zM136 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="534" 
d="M449 -189q-66 -41 -178.5 -41t-173 30t-60.5 109q0 63 72 119q-37 25 -37 76q0 20 30 66l9 14q-68 49 -68 144.5t57.5 138.5t152.5 43q43 0 85 -10l16 -3l161 5v-107l-75 6q22 -34 22 -68q0 -100 -51 -138t-160 -38q-23 0 -41 4q-10 -26 -10 -42.5t17 -22.5t80 -7
q126 -1 172 -33.5t46 -118t-66 -126.5zM169 -77q0 -43 105.5 -43t105.5 50q0 27 -18 34.5t-76 8.5l-90 7q-27 -27 -27 -57zM253.5 259q76.5 0 76.5 73.5t-76.5 73.5t-76.5 -73.5t76.5 -73.5zM103 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="611" 
d="M357 244v120h198v-353q-143 -23 -232 -23q-159 0 -218.5 85t-59.5 270t62 267t211 82q93 0 201 -21l36 -7l-4 -107q-120 13 -196.5 13t-107.5 -18t-45.5 -66.5t-14.5 -166t29 -164.5t120 -47l83 4v132h-62zM278 869q17 -16 44.5 -16t45 16t20.5 41h121
q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="534" 
d="M449 -189q-66 -41 -178.5 -41t-173 30t-60.5 109q0 63 72 119q-37 25 -37 76q0 20 30 66l9 14q-68 49 -68 144.5t57.5 138.5t152.5 43q43 0 85 -10l16 -3l161 5v-107l-75 6q22 -34 22 -68q0 -100 -51 -138t-160 -38q-23 0 -41 4q-10 -26 -10 -42.5t17 -22.5t80 -7
q126 -1 172 -33.5t46 -118t-66 -126.5zM169 -77q0 -43 105.5 -43t105.5 50q0 27 -18 34.5t-76 8.5l-90 7q-27 -27 -27 -57zM253.5 259q76.5 0 76.5 73.5t-76.5 73.5t-76.5 -73.5t76.5 -73.5zM271 664q56 0 63 66h101q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101
q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="611" 
d="M357 244v120h198v-353q-143 -23 -232 -23q-159 0 -218.5 85t-59.5 270t62 267t211 82q93 0 201 -21l36 -7l-4 -107q-120 13 -196.5 13t-107.5 -18t-45.5 -66.5t-14.5 -166t29 -164.5t120 -47l83 4v132h-62zM245 767v135h134v-135h-134z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="534" 
d="M449 -189q-66 -41 -178.5 -41t-173 30t-60.5 109q0 63 72 119q-37 25 -37 76q0 20 30 66l9 14q-68 49 -68 144.5t57.5 138.5t152.5 43q43 0 85 -10l16 -3l161 5v-107l-75 6q22 -34 22 -68q0 -100 -51 -138t-160 -38q-23 0 -41 4q-10 -26 -10 -42.5t17 -22.5t80 -7
q126 -1 172 -33.5t46 -118t-66 -126.5zM169 -77q0 -43 105.5 -43t105.5 50q0 27 -18 34.5t-76 8.5l-90 7q-27 -27 -27 -57zM253.5 259q76.5 0 76.5 73.5t-76.5 73.5t-76.5 -73.5t76.5 -73.5zM203 556v135h134v-135h-134z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="611" 
d="M357 244v120h198v-353q-143 -23 -232 -23q-159 0 -218.5 85t-59.5 270t62 267t211 82q93 0 201 -21l36 -7l-4 -107q-120 13 -196.5 13t-107.5 -18t-45.5 -66.5t-14.5 -166t29 -164.5t120 -47l83 4v132h-62zM266 -77h130l-55 -234h-121z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="534" 
d="M346 817l-46 -234h-130l55 234h121zM449 -189q-66 -41 -178.5 -41t-173 30t-60.5 109q0 63 72 119q-37 25 -37 76q0 20 30 66l9 14q-68 49 -68 144.5t57.5 138.5t152.5 43q43 0 85 -10l16 -3l161 5v-107l-75 6q22 -34 22 -68q0 -100 -51 -138t-160 -38q-23 0 -41 4
q-10 -26 -10 -42.5t17 -22.5t80 -7q126 -1 172 -33.5t46 -118t-66 -126.5zM169 -77q0 -43 105.5 -43t105.5 50q0 27 -18 34.5t-76 8.5l-90 7q-27 -27 -27 -57zM253.5 259q76.5 0 76.5 73.5t-76.5 73.5t-76.5 -73.5t76.5 -73.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="677" 
d="M465 0v282h-253v-282h-138v680h138v-278h253v278h138v-680h-138zM146 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="553" 
d="M196 0h-134v700h134v-224q69 36 124 36q100 0 138 -59t38 -182v-271h-134v268q0 63 -15 93.5t-60 30.5q-39 0 -78 -12l-13 -4v-376zM107 750l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="685" 
d="M19 486v116h59v78h138v-78h253v78h138v-78h66v-116h-66v-486h-138v282h-253v-282h-138v486h-59zM216 402h253v84h-253v-84z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="553" 
d="M12 532v116h50v52h134v-52h143v-116h-143v-56q69 36 124 36q100 0 138 -59t38 -182v-271h-134v268q0 63 -15 93.5t-60 30.5q-39 0 -78 -12l-13 -4v-376h-134v532h-50z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM225 758q-26 0 -89.5 23t-76.5 23q-25 0 -67 -32l-14 -10l-29 96q20 24 51.5 44t61 20t90.5 -23t72 -23q23 0 66 32l15 10l29 -97q-21 -24 -52 -43.5t-57 -19.5z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="258" 
d="M62 500h134v-500h-134v500zM248 602q-28 -17 -50.5 -17t-68.5 18t-59 18q-29 0 -66 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM-27 782v98h347v-98h-347z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="258" 
d="M62 500h134v-500h-134v500zM-14 597v100h287v-100h-287z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM116 869q17 -16 44.5 -16t45 16t20.5 41h121q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="258" 
d="M62 500h134v-500h-134v500zM129 664q56 0 63 66h101q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="286" 
d="M74 0v680h138v-680q-24 -15 -45.5 -40t-21.5 -41q0 -33 32 -33l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 66 68 120h-14z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="258" 
d="M62 0v500h134v-500q-24 -15 -45.5 -40t-21.5 -41q0 -33 32 -33l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 67 70 120h-12zM62 564v136h134v-136h-134z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="286" 
d="M74 0v680h138v-680h-138zM76 767v135h134v-135h-134z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="258" 
d="M62 0v500h134v-500h-134z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="302" 
d="M19 -70v120q40 0 58 15t18 56v559h137l1 -565q0 -109 -48.5 -147t-165.5 -38zM-29 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="259" 
d="M63 24v476h134v-477q0 -102 -35.5 -150.5t-142.5 -102.5l-53 99q45 30 63 47t26 40.5t8 67.5zM-37 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="591" 
d="M212 0h-138v690h138v-296l90 10l112 286h159l-148 -343l152 -347h-159l-114 271l-92 -10v-261zM245 -77h130l-55 -234h-121z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="515" 
d="M-10 -311l46 234h130l-55 -234h-121zM196 0h-134v700h134v-398l51 9l99 189h150l-130 -237l137 -263h-151l-101 195l-55 -9v-186z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="473" 
d="M461 0h-387v690h138v-557h249v-133zM110 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="270" 
d="M68 0v700h134v-700h-134zM41 861l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="472" 
d="M204 -77h130l-55 -234h-121zM461 0h-387v680h138v-558h249v-122z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="270" 
d="M68 0v700h134v-700h-134zM66 -77h130l-55 -234h-121z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="498" 
d="M461 690v-260h-130v260h130zM461 0h-387v680h138v-558h249v-122z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="377" 
d="M303 678h130l-55 -234h-121zM68 0v700h134v-700h-134z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="481" 
d="M470 0h-387v217l-42 -29l-65 90l107 75v327h138v-231l99 70l65 -90l-164 -115v-192h249v-122z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="381" 
d="M119 0v218l-51 -35l-65 90l116 81v346h134v-252l63 44l65 -90l-128 -90v-312h-134z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="693" 
d="M74 0v680h233l164 -560h10v560h138v-680h-227l-170 560h-10v-560h-138zM202 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="553" 
d="M62 0v500h133v-28l19 10q18 10 49.5 20t56.5 10q100 0 138 -59t38 -182v-271h-134v267q0 64 -15 94.5t-61 30.5t-90 -18v-374h-134zM146 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="693" 
d="M299 -77h130l-55 -234h-121zM74 0v680h233l164 -560h10v560h138v-680h-227l-170 560h-10v-560h-138z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="553" 
d="M-10 -311l46 234h130l-55 -234h-121zM196 0h-134v500h133v-28q68 40 125 40q100 0 138 -59t38 -182v-271h-134v267q0 64 -15 94.5t-60 30.5q-41 0 -79 -14l-12 -4v-374z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="693" 
d="M74 0v680h233l164 -560h10v560h138v-680h-227l-170 560h-10v-560h-138zM289 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="553" 
d="M196 0h-134v500h133v-28q68 40 125 40q100 0 138 -59t38 -182v-271h-134v267q0 64 -15 94.5t-60 30.5q-41 0 -79 -14l-12 -4v-374zM231 580l-126 151h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="693" 
d="M74 0v680h233l164 -560h10v560h138v-707q0 -109 -48.5 -147t-165.5 -38v120q40 0 58 15t18 56v21h-89l-170 560h-10v-560h-138z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="552" 
d="M195 0h-134v499h134v-22q82 35 129 35q93 0 132.5 -64.5t39.5 -186.5v-256q0 -105 -33.5 -153t-144.5 -100l-60 111q69 38 86.5 60.5t17.5 76.5v261q0 128 -66 128q-29 0 -83 -13l-18 -5v-371z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM149 782v98h347v-98h-347z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM126 597v100h287v-100h-287z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM283 869q17 -16 44.5 -16t45 16t20.5 41h121
q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM268 664q56 0 63 66h101q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101
q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="652" 
d="M326 108q81 0 110.5 52.5t29.5 176t-30 179.5t-110 56t-110 -56t-30 -179.5t29.5 -176t110.5 -52.5zM326 -12q-155 0 -218.5 84.5t-63.5 264.5t63.5 267.5t218.5 87.5t218.5 -87.5t63.5 -267.5t-63.5 -264.5t-218.5 -84.5zM366 775l67 197l116 -42l-78 -191zM158 776
l67 197l116 -42l-78 -191z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM257 628l117 190l109 -67l-125 -188zM53 628l117 189l109 -66l-125 -188z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="902" 
d="M861 0h-436q-76 -12 -119 -12q-151 0 -205.5 83.5t-54.5 276.5t58.5 273.5t201.5 80.5q43 0 117 -12h438v-132h-302v-143h242v-131h-242v-152h302v-132zM330 124q25 0 93 6v430q-70 6 -111 6t-69.5 -17.5t-41.5 -65t-13 -158.5t27.5 -156t114.5 -45z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="834" 
d="M519 130q23 -19 68 -19q68 0 166 7l28 2l2 -104q-115 -28 -215.5 -28t-145.5 68q-48 -68 -164 -68t-169 69t-53 193t53 192.5t177 68.5q58 0 97 -18.5t65 -62.5q26 45 63.5 63t89.5 18q218 0 218 -225l-10 -94h-294q1 -43 24 -62zM266 109q53 0 73.5 34t20.5 107
t-20.5 106.5t-73.5 33.5t-73.5 -33.5t-20.5 -106.5t20.5 -107t73.5 -34zM494 295h173q0 53 -19.5 75t-66.5 22t-67 -23.5t-20 -73.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="620" 
d="M212 228v-228h-138v680h264q240 0 240 -224q0 -133 -100 -196l97 -260h-151l-79 228h-133zM411.5 533q-25.5 29 -73.5 29h-126v-216h128q49 0 73 30t24 79t-25.5 78zM170 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="372" 
d="M62 0v500h133v-53q84 50 160 65v-135q-81 -17 -139 -35l-20 -7v-335h-134zM48 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="620" 
d="M211 -311l46 234h130l-55 -234h-121zM212 228v-228h-138v680h264q240 0 240 -224q0 -133 -100 -196l97 -260h-151l-79 228h-133zM413 376q24 30 24 79t-25.5 78t-73.5 29h-126v-216h128q49 0 73 30z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="372" 
d="M62 -77h130l-55 -234h-121zM62 0v500h133v-53q84 50 160 65v-135q-81 -17 -139 -35l-20 -7v-335h-134z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="620" 
d="M212 228v-228h-138v680h264q240 0 240 -224q0 -133 -100 -196l97 -260h-151l-79 228h-133zM413 376q24 30 24 79t-25.5 78t-73.5 29h-126v-216h128q49 0 73 30zM241 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="372" 
d="M62 0v500h133v-53q84 50 160 65v-135q-81 -17 -139 -35l-20 -7v-335h-134zM131 580l-126 151h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="544" 
d="M283 572q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -124.5q0 -107 -65 -162t-170 -55q-78 0 -191 24l-36 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152t170 50q74 0 188 -20l36 -7
l-11 -109q-141 16 -199 16zM144 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="472" 
d="M421 375q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -162 -201 -162q-66 0 -160 18l-32 6l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19l33 -6zM91 651l252 102
l40 -115l-262 -74z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="544" 
d="M283 572q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -124.5q0 -107 -65 -162t-170 -55q-78 0 -191 24l-36 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152t170 50q74 0 188 -20l36 -7
l-11 -109q-141 16 -199 16zM84 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="472" 
d="M421 375q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -162 -201 -162q-66 0 -160 18l-32 6l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19l33 -6zM68 580l126 151h84
l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="544" 
d="M404.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-44 0 -79 8l-14 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v93q-76 2 -180 24l-33 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152t170 50
q74 0 188 -20l36 -7l-11 -109q-141 16 -199 16q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -130.5t-53.5 -145t-142.5 -64v-27q64 -1 93.5 -19z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="472" 
d="M349.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-43 0 -80 8l-13 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v95q-63 4 -135 18l-24 4l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19l33 -6
l-2 -111q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -155 -181 -161v-26q64 -1 93.5 -19z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="544" 
d="M283 572q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -124.5q0 -107 -65 -162t-170 -55q-78 0 -191 24l-36 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152t170 50q74 0 188 -20l36 -7
l-11 -109q-141 16 -199 16zM235 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="472" 
d="M421 375q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -162 -201 -162q-66 0 -160 18l-32 6l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19l33 -6zM203 580l-126 151
h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="Tcedilla" unicode="&#x162;" horiz-adv-x="0" 
 />
    <glyph glyph-name="tcedilla" unicode="&#x163;" horiz-adv-x="364" 
d="M254.5 -56q29.5 -18 29.5 -73t-29 -83.5t-82 -28.5q-44 0 -79 8l-14 3l4 78q31 -1 48 -1q38 0 38 26q0 22 -38 22h-24v106h45q-40 16 -55.5 57t-15.5 120v208h-59v114h59v139h134v-139h123v-114h-123v-205q0 -30 1.5 -42.5t11 -21.5t29.5 -9l76 2l6 -107
q-67 -15 -108.5 -15t-70.5 10v-35q64 -1 93.5 -19z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="526" 
d="M13 558v122h500v-122h-180v-558h-138v558h-182zM206 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="487" 
d="M340 378h-123v-189q0 -30 1.5 -42.5t11 -21.5t29.5 -9l76 2l6 -115q-67 -15 -102 -15q-90 0 -123 40.5t-33 149.5v200h-59v121h59v140h134v-140h123v-121zM506 690v-260h-130v260h130z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="528" 
d="M14 558v122h500v-122h-180v-189h139v-116h-139v-253h-138v253h-132v116h132v189h-182z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="365" 
d="M339 378h-123v-69h95v-100h-95v-20q0 -30 1.5 -42.5t11 -21.5t29.5 -9l76 2l6 -115q-67 -15 -102 -15q-90 0 -123 40.5t-33 149.5v31h-30v100h30v69h-59v121h59v140h134v-140h123v-121z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM404 758q-26 0 -89.5 23t-76.5 23q-25 0 -67 -32l-14 -10l-29 96q20 24 51.5 44t61 20t90.5 -23t72 -23q23 0 67 32l14 10l29 -97
q-21 -24 -52 -43.5t-57 -19.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM385 602q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25
l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM157 782v98h347v-98h-347z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM133 597v100h287v-100h-287z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM287 869q17 -16 44.5 -16t45 16t20.5 41h121q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM276 664q56 0 63 66h101q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM481 839q0 -59 -43 -92.5t-107 -33.5t-107 33.5t-43 92.5t43 92.5t107 33.5t107 -33.5t43 -92.5zM272 839q0 -19 16 -30t43 -11t43 11t16 30
t-16 30t-43 11t-43 -11t-16 -30z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM192.5 554.5q-33.5 33.5 -33.5 83t33.5 83.5t83 34t83.5 -34t34 -83.5t-34 -83t-83.5 -33.5t-83 33.5zM231 638q0 -20 13 -33
t33 -13t32.5 13t12.5 33t-13 32.5t-32.5 12.5t-32.5 -12.5t-13 -32.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="654" 
d="M206 227q0 -119 121 -119t121 119v453h138v-450q0 -125 -64.5 -183.5t-194.5 -58.5t-194.5 58.5t-64.5 183.5v450h138v-453zM361 775l67 197l116 -42l-78 -191zM153 776l67 197l116 -42l-78 -191z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="553" 
d="M491 500v-500h-133v28l-20 -10q-19 -10 -50 -20t-55 -10q-104 0 -140 57.5t-36 192.5v262h134v-264q0 -74 12 -101t60 -27t94 18v374h134zM300 628l117 190l109 -67l-125 -188zM96 628l117 189l109 -66l-125 -188z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="656" 
d="M207 227q0 -119 121 -119t121 119v453h138v-450q0 -197 -167 -233q-53 -45 -53 -78t32 -33l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 60 53 109q-113 7 -169.5 65t-56.5 176v450h138v-453z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="553" 
d="M357 499h134v-499q-8 -4 -20 -11t-31.5 -28.5t-19.5 -41.5q0 -33 32 -33l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 70q0 66 74 120h-12v21q-78 -33 -124 -33q-105 0 -140.5 57t-35.5 193v261h134v-261q0 -76 11.5 -102.5t58.5 -26.5q26 0 79 13l17 4v373z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="930" 
d="M19 680h145l84 -562h15l122 562h160l122 -562h15l84 562h145l-130 -680h-209l-107 517l-107 -517h-209zM273 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="772" 
d="M24 500h132l61 -386h24l76 376h138l76 -376h24l61 386h132l-95 -500h-210l-57 306l-57 -306h-210zM219 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="557" 
d="M348 0h-138v275l-210 405h153l125 -271l125 271h153l-208 -405v-275zM88 765l134 149h117l134 -149h-142l-49 56l-52 -56h-142z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="500" 
d="M16 500h132l91 -386h23l91 386h132l-176 -710h-131l55 210h-94zM82 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="557" 
d="M348 0h-138v275l-210 405h153l125 -271l125 271h153l-208 -405v-275zM105 764v132h128v-132h-128zM319 764v132h128v-132h-128z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="532" 
d="M41 560v120h450v-120l-284 -422v-18h284v-120h-450v119l284 423v18h-284zM121 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="454" 
d="M41 500h371v-120l-210 -260h210v-120h-371v120l210 260h-210v120zM82 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="532" 
d="M41 560v120h450v-120l-284 -422v-18h284v-120h-450v119l284 423v18h-284zM199 767v135h134v-135h-134z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="454" 
d="M41 500h371v-120l-210 -260h210v-120h-371v120l210 260h-210v120zM159 556v135h134v-135h-134z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="532" 
d="M41 560v120h450v-120l-284 -422v-18h284v-120h-450v119l284 423v18h-284zM210 765l-134 149h142l52 -56l49 56h142l-134 -149h-117z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="454" 
d="M41 380v120h371v-120l-210 -260h210v-120h-371v120l210 260h-210zM194 580l-126 151h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M321 -47q0 -103 -39.5 -143t-128.5 -40q-51 0 -93 10l-16 3v110q64 -3 92 -3q51 0 51 66v430h-54v114h54v24q0 107 31.5 147.5t114.5 40.5q36 0 91 -10l21 -3v-109q-44 2 -74 2t-40 -15.5t-10 -53.5v-23h121v-114h-121v-433z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="602" 
d="M450 721q0 -33 -14 -58l149 -663h-138l-27 123h-238l-27 -123h-138l148 660q-15 26 -15 61q0 59 43 92.5t107 33.5t107 -33.5t43 -92.5zM277 566l-69 -323h186l-69 323h-48zM142 939l252 106l41 -120l-260 -79zM241 721q0 -19 16 -30t43 -11h5q25 1 39.5 12t14.5 29.5
t-16 29.5t-43 11t-43 -11t-16 -30z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM168.5 554.5q-33.5 33.5 -33.5 83t33.5 83.5t83 34t83.5 -34t34 -83.5t-34 -83t-83.5 -33.5t-83 33.5zM207 638q0 -20 13 -33t33 -13t32.5 13t12.5 33t-13 32.5t-32.5 12.5t-32.5 -12.5t-13 -32.5zM108 852l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="872" 
d="M393 0v116h-213l-29 -116h-138l184 690h634v-132h-302v-143h242v-131h-242v-152h302v-132h-438zM294 558l-80 -309h179l1 309h-100zM365 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="784" 
d="M469 130q23 -19 68 -19q68 0 166 7l28 2l2 -104q-115 -28 -212.5 -28t-148.5 48l-23 -11q-84 -37 -164 -37t-118 43.5t-38 122t43.5 113t130.5 39.5l108 7v25q0 20 -14 32.5t-41 12.5q-72 0 -157 -8l-32 -3l-4 117q133 22 212.5 22t122.5 -37q52 37 133 37
q218 0 218 -225l-10 -94h-294q1 -43 24 -62zM311 118l-1 93l-90 -4q-56 -3 -56 -51q0 -53 41 -53q40 0 89 11zM445 295h172q0 53 -19.5 75t-67 22t-66.5 -23t-19 -74zM256 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="654" 
d="M327 -12q-49 0 -96 10l-58 -125l-98 49l58 124q-88 78 -88 290q0 181 63.5 268.5t218.5 87.5q57 0 101 -12l61 130l100 -43l-65 -140q85 -82 85 -291q0 -179 -63.5 -263.5t-218.5 -84.5zM217 516q-30 -56 -30 -164.5t15 -157.5l173 371q-25 7 -48 7q-80 0 -110 -56z
M327 108q81 0 110.5 52.5t29.5 158.5t-14 156l-169 -363q17 -4 43 -4zM170 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="532" 
d="M266 512q28 0 56 -5l40 100l81 -30l-41 -98q94 -59 94 -228q0 -263 -230 -263q-34 0 -63 6l-41 -102l-80 30l43 103q-89 60 -89 226q0 124 53 192.5t177 68.5zM121 651l252 102l40 -115l-262 -74zM266 102q53 0 73.5 36t20.5 107t-14 105l-100 -247q6 -1 20 -1zM266 398
q-53 0 -73.5 -35t-20.5 -105t12 -102l95 241q-2 0 -6.5 0.5t-6.5 0.5z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="544" 
d="M215 -77h130l-55 -234h-121zM283 572q-109 0 -109 -72q0 -32 27 -49t126.5 -49t139.5 -72.5t40 -124.5q0 -107 -65 -162t-170 -55q-78 0 -191 24l-36 7l14 107q134 -18 205 -18q106 0 106 88q0 32 -24.5 50t-97.5 39q-116 33 -163.5 78.5t-47.5 126.5q0 102 62 152
t170 50q74 0 188 -20l36 -7l-11 -109q-141 16 -199 16z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="472" 
d="M29 -77h130l-55 -234h-121zM421 375q-124 16 -173.5 16t-64 -9t-14.5 -28.5t19.5 -27t100 -22t114.5 -47.5t34 -107q0 -162 -201 -162q-66 0 -160 18l-32 6l4 112q124 -16 172.5 -16t66 9.5t17.5 28.5t-18.5 28t-96 22t-115.5 44t-38 110.5t54 120t139 40.5q59 0 161 -19
l33 -6z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="526" 
d="M223 -77h130l-55 -234h-121zM13 558v122h500v-122h-180v-558h-138v558h-182z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="364" 
d="M49 -77h130l-55 -234h-121zM339 386h-123v-205q0 -30 1.5 -42.5t11 -21.5t29.5 -9l76 2l6 -107q-67 -15 -102 -15q-90 0 -123 40.5t-33 149.5v208h-59v114h59v139h134v-139h123v-114z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="268" 
d="M-24 580l126 151h84l128 -151h-123l-46 67l-46 -67h-123z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="268" 
d="M106 580l-126 151h123l46 -67l46 67h123l-128 -151h-84z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="268" 
d="M144 664q56 0 63 66h101q-8 -65 -50.5 -111t-113.5 -46t-113.5 46t-50.5 111h101q4 -31 20.5 -48.5t42.5 -17.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="268" 
d="M67 556v135h134v-135h-134z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="268" 
d="M53.5 554.5q-33.5 33.5 -33.5 83t33.5 83.5t83 34t83.5 -34t34 -83.5t-34 -83t-83.5 -33.5t-83 33.5zM92 638q0 -20 13 -33t33 -13t32.5 13t12.5 33t-12.5 32.5t-32.5 12.5t-33 -12.5t-13 -32.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="474" 
d="M402 1q-58 -47 -58 -81t32 -34l42 4l12 -95q-59 -11 -104.5 -11t-76 26t-30.5 66t20.5 72.5t40.5 47.5l20 15z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="268" 
d="M264 602q-28 -17 -50.5 -17t-68.5 18t-59 18q-29 0 -66 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="268" 
d="M154 628l117 190l109 -67l-125 -188zM-50 628l117 189l109 -66l-125 -188z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M320 154v232h-85l-26 -386h-135l32 386q-32 0 -73 -10l-15 -3v109q64 18 150 18h251q51 0 95 9l14 3v-110q-24 -11 -74 -14v-234q0 -27 14 -38.5t55 -11.5v-114q-70 0 -108.5 6t-60.5 26.5t-28 49t-6 82.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="930" 
d="M19 680h145l84 -562h15l122 562h160l122 -562h15l84 562h145l-130 -680h-209l-107 517l-107 -517h-209zM368 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="772" 
d="M24 500h132l61 -386h24l76 376h138l76 -376h24l61 386h132l-95 -500h-210l-57 306l-57 -306h-210zM264 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="930" 
d="M19 680h145l84 -562h15l122 562h160l122 -562h15l84 562h145l-130 -680h-209l-107 517l-107 -517h-209zM329 839l252 106l41 -120l-260 -79z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="772" 
d="M24 500h132l61 -386h24l76 376h138l76 -376h24l61 386h132l-95 -500h-210l-57 306l-57 -306h-210zM251 651l252 102l40 -115l-262 -74z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="930" 
d="M19 680h145l84 -562h15l122 562h160l122 -562h15l84 562h145l-130 -680h-209l-107 517l-107 -517h-209zM292 764v132h128v-132h-128zM506 764v132h128v-132h-128z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="772" 
d="M24 500h132l61 -386h24l76 376h138l76 -376h24l61 386h132l-95 -500h-210l-57 306l-57 -306h-210zM216 596v132h128v-132h-128zM432 596v132h128v-132h-128z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="511" 
d="M445 344v-208q2 -24 8 -33.5t25 -13.5l-4 -101q-51 0 -81.5 7t-61.5 28q-72 -35 -147 -35q-155 0 -155 164q0 80 43 113.5t132 39.5l107 8v31q0 31 -14 42.5t-45 11.5l-188 -8l-4 93q107 29 203.5 29t139 -40t42.5 -128zM220 212q-56 -5 -56 -60t49 -55q38 0 83 12l15 4
v107zM66 550l126 151h84l128 -151h-123l-46 67l-46 -67h-123zM347 762q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="600" 
d="M17 0l153 690h262l150 -690h-138l-31 116h-232l-26 -116h-138zM277 563l-67 -314h177l-65 314h-45zM260 829q17 -16 44.5 -16t45 16t20.5 41h121q-8 -68 -54.5 -109.5t-132.5 -41.5t-132 41.5t-54 109.5h121q4 -25 21 -41zM143 1114l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="511" 
d="M196 130q23 -19 68 -19q68 0 166 7l28 2l2 -104q-115 -28 -208 -28q-113 0 -164 60t-51 195q0 268 221 268q218 0 218 -225l-10 -94h-294q1 -43 24 -62zM344 295q0 53 -19.5 75t-66.5 22t-66.5 -23t-20.5 -74h173zM95 550l126 151h84l128 -151h-123l-46 67l-46 -67h-123z
M369 762q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="532" 
d="M266 512q124 0 177 -68.5t53 -192.5q0 -263 -230 -263t-230 263q0 124 53 192.5t177 68.5zM266 102q53 0 73.5 36t20.5 113t-20.5 112t-73.5 35t-73.5 -35t-20.5 -112t20.5 -113t73.5 -36zM98 550l126 151h84l128 -151h-123l-46 67l-46 -67h-123zM369 762
q-28 -17 -50.5 -17t-68.5 18t-60 18q-28 0 -65 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="557" 
d="M348 0h-138v275l-210 405h153l125 -271l125 271h153l-208 -405v-275zM182 945l252 -106l-33 -93l-260 79z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="500" 
d="M16 500h132l91 -386h23l91 386h132l-176 -710h-131l55 210h-94zM120 753l252 -102l-30 -87l-262 74z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="541" 
d="M341 0h-138v295l-208 395h153l123 -241l122 241h153l-205 -396v-294zM384 793q-28 -17 -50.5 -17t-68.5 18t-59 18q-25 0 -66 -24l-13 -7l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 26l14 8l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="500" 
d="M16 499h132l91 -368h23l91 368h132l-176 -709h-131l51 210h-90zM364 602q-28 -17 -50.5 -17t-68.5 18t-59 18q-29 0 -66 -23l-13 -8l-29 75q20 23 50.5 41.5t53 18.5t69 -18t58.5 -18q24 0 66 25l14 9l29 -76q-6 -7 -16 -17.5t-38 -27.5z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="614" 
d="M57 323h500v-114h-500v114z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1114" 
d="M57 323h1000v-114h-1000v114z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="259" 
d="M223 679l-48 -247h-140l85 247h103z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="254" 
d="M41 433l47 247h141l-85 -247h-103z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="256" 
d="M23 -117l47 247h141l-85 -247h-103z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="443" 
d="M407 679l-48 -247h-140l85 247h103zM223 679l-48 -247h-140l85 247h103z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="439" 
d="M41 434l47 247h141l-86 -247h-102zM225 434l47 247h141l-85 -247h-103z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="421" 
d="M181 117l-48 -247h-140l85 247h103zM371 117l-48 -247h-140l85 247h103z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="480" 
d="M28 386v114h145v180h134v-180h145v-114h-145l-10 -457h-114l-10 457h-145z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="512" 
d="M189 -71v180h-145v114h145v163h-145v114h145v180h134v-180h144v-114h-144v-163h145v-114h-145v-180h-134z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="450" 
d="M100 100v280h250v-280h-250z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="766" 
d="M56 0v166h144v-166h-144zM311 0v166h144v-166h-144zM566 0v166h144v-166h-144z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="845" 
d="M149 592q-13 0 -17.5 -14.5t-4.5 -50.5t4.5 -51t17.5 -15t17.5 15t4.5 51t-4.5 50.5t-17.5 14.5zM140 -8l235 707l73 -26l-235 -705zM149 672q120 0 120 -145.5t-120 -145.5t-120 145.5t120 145.5zM441 -12q-120 0 -120 145.5t120 145.5t120 -145.5t-120 -145.5zM419 134
q0 -36 4.5 -51t17.5 -15t17.5 15t4.5 51t-4.5 50.5t-17.5 14.5t-17.5 -14.5t-4.5 -50.5zM698 -12q-120 0 -120 145.5t120 145.5t120 -145.5t-120 -145.5zM676 134q0 -36 4.5 -51t17.5 -15t17.5 15t4.5 51t-4.5 50.5t-17.5 14.5t-17.5 -14.5t-4.5 -50.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="318" 
d="M263 319l-109 -75l109 -86v-135l-223 171v96l223 164v-135z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="318" 
d="M163 254l-109 75v135l223 -164v-96l-223 -171v135z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="10" 
d="M-224 29l393 632l53 -35l-393 -633z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="280" 
d="M140 801q66 0 97.5 -38t31.5 -130.5t-31 -129t-98 -36.5t-98 36.5t-31 129t31.5 130.5t97.5 38zM140 706q-7 0 -11 -4.5t-6 -21t-2 -48.5t2.5 -47.5t6 -19t12.5 -3.5t13 12t4 58.5t-4 60t-15 13.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="280" 
d="M146 478v35h-132v88l39 189h118l-52 -182h27l14 90h91v-90h11v-95h-11v-35h-105z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="280" 
d="M251 790v-90h-124l-3 -25q28 6 44 6q93 0 93 -99q0 -111 -118 -111q-46 0 -100 11l-18 3l8 85q56 -9 87 -9t31 15.5t-14 15.5l-26 -4l-78 11l11 191h207z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="280" 
d="M124 682q30 5 38 5q54 0 79 -26t25 -79.5t-33.5 -84t-85.5 -30.5q-131 0 -131 155q0 89 30.5 134t106.5 45q32 0 86 -9l18 -3l-6 -87q-66 6 -96.5 6t-30.5 -26zM143 601q-11 0 -19 -8q0 -34 17.5 -34t17.5 21t-16 21z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="280" 
d="M26 685v105h227v-101l-95 -221l-121 19l91 185v13h-102z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="280" 
d="M17 712q0 89 123.5 89t123.5 -90q0 -38 -20 -63l-7 -8q13 -9 22 -27.5t9 -40.5q0 -57 -32 -81t-96.5 -24t-96 24t-31.5 81q0 20 8 37t16 25l8 7q-27 20 -27 71zM140 552q19 0 19 22q0 13 -12 25h-14q-12 -12 -12 -25q0 -22 19 -22zM140 716q-18 0 -18 -19q0 -12 11 -25
h14q11 13 11 25q0 19 -18 19z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="280" 
d="M152 585q-20 -4 -35 -4q-102 0 -102 104q0 55 32 85.5t87 30.5q131 0 131 -165q0 -87 -30 -128t-101 -41q-42 0 -92 11l-16 3l6 87q49 -6 84.5 -6t35.5 23zM138 708q-16 0 -16 -22.5t16 -22.5l19 6q0 39 -19 39z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="280" 
d="M140 223q66 0 97.5 -38t31.5 -130.5t-31 -129t-98 -36.5t-98 36.5t-31 129t31.5 130.5t97.5 38zM140 128q-7 0 -11 -4.5t-6 -21t-2 -48.5t2.5 -47.5t6 -19t12.5 -3.5t13 12t4 58.5t-4 60t-15 13.5z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="280" 
d="M227 212v-312h-106v195l-47 -32l-49 74l106 75h96z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="280" 
d="M257 -100h-231v90l73 59q46 37 46 57q0 15 -37 15l-76 -6l-4 99q73 9 126 9t78 -23t25 -66.5t-13 -66t-43 -44.5l-38 -28h94v-95z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="280" 
d="M131 223q121 0 121 -89q0 -53 -29 -70q35 -15 35 -70q0 -105 -118 -105l-121 8l6 93q61 -6 92 -6t31 16q0 14 -26 14h-66v85h64q20 0 20 14.5t-27 14.5l-84 -5l-6 91q68 9 108 9z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="280" 
d="M150 -100v35h-132v88l39 189h118l-52 -182h27l14 90h91v-90h11v-95h-11v-35h-105z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="280" 
d="M244 212v-90h-124l-3 -25q28 6 44 6q93 0 93 -99q0 -111 -118 -111q-46 0 -100 11l-18 3l8 85q56 -9 87 -9t31 15.5t-14 15.5l-26 -4l-78 11l11 191h207z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="280" 
d="M120 104q30 5 38 5q54 0 79 -26t25 -79.5t-33.5 -84t-85.5 -30.5q-131 0 -131 155q0 89 30.5 134t106.5 45q32 0 86 -9l18 -3l-6 -87q-66 6 -96.5 6t-30.5 -26zM139 23q-11 0 -19 -8q0 -34 17.5 -34t17.5 21t-16 21z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="280" 
d="M26 107v105h227v-101l-95 -221l-121 19l91 185v13h-102z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="280" 
d="M16 134q0 89 123.5 89t123.5 -90q0 -20 -6.5 -37.5t-13.5 -25.5l-7 -8q13 -9 22 -27.5t9 -40.5q0 -57 -32 -81t-96.5 -24t-96 24t-31.5 81q0 20 8 37t16 25l8 7q-27 20 -27 71zM139 -26q19 0 19 22q0 13 -12 25h-14q-12 -12 -12 -25q0 -22 19 -22zM139 138q-18 0 -18 -19
q0 -12 11 -25h14q11 13 11 25q0 19 -18 19z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="280" 
d="M151 7q-20 -4 -35 -4q-102 0 -102 104q0 55 32 85.5t87 30.5q131 0 131 -165q0 -87 -30 -128t-101 -41q-42 0 -91 11l-17 3l6 87q49 -6 84.5 -6t35.5 23zM137 130q-16 0 -16 -22.5t16 -22.5l19 6q0 39 -19 39z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M26 355v102h62q15 117 70.5 166t174.5 49q77 0 193 -26l-4 -107q-88 14 -157.5 14t-97.5 -20t-40 -76h235v-102h-243v-66h243v-102h-231q12 -45 40 -62.5t90 -17.5t161 13l3 -109q-101 -23 -203.5 -23t-158.5 48t-73 151h-64v102h55v66h-55z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="660" 
d="M80 565v79h200v-79h-41v-233h-88v233h-71zM305 331v313h103l41 -158l45 158h102v-313h-82v174l-34 -153h-55l-38 153v-174h-82z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" 
d="M52 105h80q-9 9 -28.5 43t-31.5 64q-32 82 -32 173q0 151 56 219t184 68t184 -68t56 -219q0 -76 -23 -146t-46 -102l-23 -32h80v-114h-208v99q29 49 53.5 126.5t24.5 133.5q0 116 -18.5 159t-79.5 43t-79.5 -43t-18.5 -159q0 -56 19.5 -121t38.5 -102l20 -37v-99h-208
v114z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M262 712q135 0 195.5 -89t60.5 -274t-60 -271.5t-195 -86.5q-235 0 -235 225q0 106 57 164t162 58q61 0 111 -18l16 -6q-6 105 -34 144.5t-101 39.5q-32 0 -70.5 -8.5t-60.5 -17.5l-23 -8l-4 103q85 45 181 45zM264 324q-92 0 -92 -109.5t91 -109.5q56 0 83.5 46.5
t27.5 150.5q-38 22 -110 22z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" 
d="M516 0h-472v104l124 556h225l123 -558v-102zM294 548h-27l-90 -434h206z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M335 -180v820h-113v-820h-138v820h-53v120h499v-120h-57v-820h-138z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M30 760h500v-120h-354v-15l215 -281v-93l-215 -296v-15h354v-120h-500v139l238 339l-238 322v140z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M62 191v120h436v-120h-436z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M1 320v120h166l92 -456h13l163 807h136l-200 -921h-214l-102 450h-54z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M166 121q-139 0 -139 171t137 171q75 0 116 -78q41 78 116 78q137 0 137 -171t-139 -171q-75 0 -114 77q-40 -77 -114 -77zM185 348q-36 0 -36 -56t36 -56q13 0 24 13.5t26 42.5q-28 56 -50 56zM375 236q36 0 36 56t-36 56q-22 0 -50 -56q15 -29 26 -42.5t24 -13.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M345 -16q0 -183 -149 -183q-30 0 -82 10l-15 3l4 110q25 -3 53.5 -3t39.5 14.5t11 51.5v606q0 106 33 147t116 41q33 0 87 -11l18 -3l-4 -110q-32 4 -62 4t-40 -15.5t-10 -53.5v-608z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M383.5 265q-28.5 0 -108 22t-99 22t-47 -9.5t-43.5 -19.5l-17 -10l-10 107q22 19 59.5 35.5t65 16.5t104 -22t96.5 -22q39 0 90 29l17 9l8 -107q-19 -19 -53 -35t-62.5 -16zM383.5 45q-28.5 0 -108 22t-99 22t-47 -9.5t-43.5 -19.5l-17 -10l-10 107q22 19 59.5 35.5
t65 16.5t104 -22t96.5 -22q39 0 90 29l17 9l8 -107q-19 -19 -53 -35t-62.5 -16z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M57 295v121h228l68 154l104 -41l-50 -113h94v-121h-147l-38 -87h185v-121h-238l-63 -143l-104 41l45 102h-84v121h137l38 87h-175z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M487 386l-272 -49l272 -54v-130l-421 121v116l421 126v-130zM66 19v120h421v-120h-421z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M345 337l-272 49v130l421 -126v-116l-421 -121v130zM494 139v-120h-421v120h421z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M193 0l-149 324l149 336h175l148 -336l-148 -324h-175zM289 114l94 210l-94 222h-17l-95 -222l95 -210h17z" />
    <glyph glyph-name="dotlessj" unicode="&#xf6be;" horiz-adv-x="259" 
d="M63 24v476h134v-477q0 -102 -35.5 -150.5t-142.5 -102.5l-53 99q45 30 63 47t26 40.5t8 67.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="170" 
d="M-5 -311l46 234h130l-55 -234h-121z" />
    <glyph glyph-name="questiondown.cap" horiz-adv-x="425" 
d="M74.5 27q-48.5 39 -48.5 117t15.5 113.5t65.5 73t63 57.5t13 44v31h107q31 -34 31 -91q0 -36 -63.5 -87t-78 -70.5t-14.5 -47.5q0 -59 85 -59q60 0 123 12l21 4l7 -101q-86 -35 -182 -35t-144.5 39zM312 680v-161h-144v161h144z" />
    <glyph glyph-name="endash.cap" horiz-adv-x="618" 
d="M59 353h500v-114h-500v114z" />
    <glyph glyph-name="emdash.cap" horiz-adv-x="1118" 
d="M59 353h1000v-114h-1000v114z" />
    <glyph glyph-name="periodcentered.cap" horiz-adv-x="256" 
d="M56 245v166h144v-166h-144z" />
    <glyph glyph-name="exclamdown.cap" horiz-adv-x="280" 
d="M212 681v-161h-144v161h144zM199 431l13 -430h-145l14 430h118z" />
    <glyph glyph-name="parenleft.cap" horiz-adv-x="308" 
d="M186 331q0 -88 24 -194.5t48 -168.5l23 -63h-127q-14 22 -37 74t-38.5 99t-28 116.5t-12.5 136.5t12 138t29 124q36 112 63 164l12 23h127q-34 -87 -64.5 -224t-30.5 -225z" />
    <glyph glyph-name="parenright.cap" horiz-adv-x="308" 
d="M122 359q0 88 -23.5 194.5t-47.5 168.5l-24 63h127q14 -22 37 -74t38.5 -99t28 -116.5t12.5 -136.5t-12 -138t-29 -124q-36 -112 -63 -164l-12 -23h-127q34 87 64.5 224t30.5 225z" />
    <glyph glyph-name="bracketleft.cap" horiz-adv-x="355" 
d="M320 779v-120h-115v-632h115v-120h-252v872h252z" />
    <glyph glyph-name="bracketright.cap" horiz-adv-x="355" 
d="M35 659v120h252v-872h-252v120h115v632h-115z" />
    <glyph glyph-name="braceleft.cap" horiz-adv-x="361" 
d="M251 606l7 -128q0 -63 -20.5 -90t-91.5 -44q70 -17 91.5 -47.5t21.5 -94.5l-7 -117q0 -34 14.5 -53t56.5 -22v-114q-111 4 -156.5 43t-45.5 129l7 121q0 73 -112 102v104q112 26 112 94l-7 127q0 95 45.5 133t158.5 42v-114q-43 -4 -58.5 -21t-15.5 -50z" />
    <glyph glyph-name="braceright.cap" horiz-adv-x="362" 
d="M110 77l-7 128q0 63 20.5 90t91.5 44q-70 17 -91.5 47.5t-21.5 94.5l7 117q0 34 -14.5 53t-56.5 22v114q111 -4 156.5 -43t45.5 -129l-7 -121q0 -73 112 -102v-104q-112 -26 -112 -94l7 -127q0 -95 -45.5 -133t-158.5 -42v114q43 4 58.5 21t15.5 50z" />
    <hkern u1="&#x22;" u2="&#x129;" k="-8" />
    <hkern u1="&#x22;" u2="&#xf0;" k="9" />
    <hkern u1="&#x22;" u2="&#xef;" k="-18" />
    <hkern u1="&#x22;" u2="&#xee;" k="-12" />
    <hkern u1="&#x22;" u2="&#xec;" k="-27" />
    <hkern u1="&#x22;" u2="&#xc6;" k="38" />
    <hkern u1="&#x22;" u2="&#x40;" k="10" />
    <hkern u1="&#x22;" u2="&#x2f;" k="64" />
    <hkern u1="&#x22;" u2="&#x26;" k="21" />
    <hkern u1="&#x26;" u2="&#x201d;" k="34" />
    <hkern u1="&#x26;" u2="&#x2019;" k="34" />
    <hkern u1="&#x26;" u2="&#x1ef8;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="47" />
    <hkern u1="&#x26;" u2="&#x1e84;" k="13" />
    <hkern u1="&#x26;" u2="&#x1e82;" k="13" />
    <hkern u1="&#x26;" u2="&#x1e80;" k="13" />
    <hkern u1="&#x26;" u2="&#x21a;" k="33" />
    <hkern u1="&#x26;" u2="&#x178;" k="47" />
    <hkern u1="&#x26;" u2="&#x176;" k="47" />
    <hkern u1="&#x26;" u2="&#x174;" k="13" />
    <hkern u1="&#x26;" u2="&#x166;" k="33" />
    <hkern u1="&#x26;" u2="&#x164;" k="33" />
    <hkern u1="&#x26;" u2="&#xdd;" k="47" />
    <hkern u1="&#x26;" u2="Y" k="47" />
    <hkern u1="&#x26;" u2="W" k="13" />
    <hkern u1="&#x26;" u2="V" k="23" />
    <hkern u1="&#x26;" u2="T" k="33" />
    <hkern u1="&#x26;" u2="&#x27;" k="38" />
    <hkern u1="&#x26;" u2="&#x22;" k="38" />
    <hkern u1="&#x27;" u2="&#x129;" k="-8" />
    <hkern u1="&#x27;" u2="&#xf0;" k="9" />
    <hkern u1="&#x27;" u2="&#xef;" k="-18" />
    <hkern u1="&#x27;" u2="&#xee;" k="-12" />
    <hkern u1="&#x27;" u2="&#xec;" k="-27" />
    <hkern u1="&#x27;" u2="&#xc6;" k="38" />
    <hkern u1="&#x27;" u2="&#x40;" k="10" />
    <hkern u1="&#x27;" u2="&#x2f;" k="64" />
    <hkern u1="&#x27;" u2="&#x26;" k="21" />
    <hkern u1="&#x28;" u2="&#x1ef9;" k="10" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="10" />
    <hkern u1="&#x28;" u2="&#x1ed7;" k="20" />
    <hkern u1="&#x28;" u2="&#x1ec5;" k="20" />
    <hkern u1="&#x28;" u2="&#x1eab;" k="12" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="14" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="14" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="14" />
    <hkern u1="&#x28;" u2="&#x219;" k="10" />
    <hkern u1="&#x28;" u2="&#x1ff;" k="20" />
    <hkern u1="&#x28;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x28;" u2="&#x1fd;" k="12" />
    <hkern u1="&#x28;" u2="&#x1fb;" k="12" />
    <hkern u1="&#x28;" u2="&#x177;" k="10" />
    <hkern u1="&#x28;" u2="&#x175;" k="14" />
    <hkern u1="&#x28;" u2="&#x173;" k="15" />
    <hkern u1="&#x28;" u2="&#x171;" k="15" />
    <hkern u1="&#x28;" u2="&#x16f;" k="15" />
    <hkern u1="&#x28;" u2="&#x16d;" k="15" />
    <hkern u1="&#x28;" u2="&#x16b;" k="15" />
    <hkern u1="&#x28;" u2="&#x169;" k="15" />
    <hkern u1="&#x28;" u2="&#x161;" k="10" />
    <hkern u1="&#x28;" u2="&#x15f;" k="10" />
    <hkern u1="&#x28;" u2="&#x15d;" k="10" />
    <hkern u1="&#x28;" u2="&#x15b;" k="10" />
    <hkern u1="&#x28;" u2="&#x159;" k="11" />
    <hkern u1="&#x28;" u2="&#x157;" k="11" />
    <hkern u1="&#x28;" u2="&#x155;" k="11" />
    <hkern u1="&#x28;" u2="&#x153;" k="20" />
    <hkern u1="&#x28;" u2="&#x152;" k="15" />
    <hkern u1="&#x28;" u2="&#x151;" k="20" />
    <hkern u1="&#x28;" u2="&#x150;" k="15" />
    <hkern u1="&#x28;" u2="&#x14f;" k="20" />
    <hkern u1="&#x28;" u2="&#x14e;" k="15" />
    <hkern u1="&#x28;" u2="&#x14d;" k="20" />
    <hkern u1="&#x28;" u2="&#x14c;" k="15" />
    <hkern u1="&#x28;" u2="&#x14b;" k="11" />
    <hkern u1="&#x28;" u2="&#x148;" k="11" />
    <hkern u1="&#x28;" u2="&#x146;" k="11" />
    <hkern u1="&#x28;" u2="&#x144;" k="11" />
    <hkern u1="&#x28;" u2="&#x135;" k="-22" />
    <hkern u1="&#x28;" u2="&#x12d;" k="-25" />
    <hkern u1="&#x28;" u2="&#x129;" k="-7" />
    <hkern u1="&#x28;" u2="&#x122;" k="15" />
    <hkern u1="&#x28;" u2="&#x120;" k="15" />
    <hkern u1="&#x28;" u2="&#x11e;" k="15" />
    <hkern u1="&#x28;" u2="&#x11c;" k="15" />
    <hkern u1="&#x28;" u2="&#x11b;" k="20" />
    <hkern u1="&#x28;" u2="&#x119;" k="20" />
    <hkern u1="&#x28;" u2="&#x117;" k="20" />
    <hkern u1="&#x28;" u2="&#x115;" k="20" />
    <hkern u1="&#x28;" u2="&#x113;" k="20" />
    <hkern u1="&#x28;" u2="&#x111;" k="19" />
    <hkern u1="&#x28;" u2="&#x10f;" k="19" />
    <hkern u1="&#x28;" u2="&#x10d;" k="20" />
    <hkern u1="&#x28;" u2="&#x10c;" k="14" />
    <hkern u1="&#x28;" u2="&#x10b;" k="20" />
    <hkern u1="&#x28;" u2="&#x10a;" k="14" />
    <hkern u1="&#x28;" u2="&#x109;" k="20" />
    <hkern u1="&#x28;" u2="&#x108;" k="14" />
    <hkern u1="&#x28;" u2="&#x107;" k="20" />
    <hkern u1="&#x28;" u2="&#x106;" k="14" />
    <hkern u1="&#x28;" u2="&#x105;" k="12" />
    <hkern u1="&#x28;" u2="&#x103;" k="12" />
    <hkern u1="&#x28;" u2="&#x101;" k="12" />
    <hkern u1="&#x28;" u2="&#xff;" k="10" />
    <hkern u1="&#x28;" u2="&#xfd;" k="10" />
    <hkern u1="&#x28;" u2="&#xfc;" k="15" />
    <hkern u1="&#x28;" u2="&#xfb;" k="15" />
    <hkern u1="&#x28;" u2="&#xfa;" k="15" />
    <hkern u1="&#x28;" u2="&#xf9;" k="15" />
    <hkern u1="&#x28;" u2="&#xf8;" k="20" />
    <hkern u1="&#x28;" u2="&#xf6;" k="20" />
    <hkern u1="&#x28;" u2="&#xf5;" k="20" />
    <hkern u1="&#x28;" u2="&#xf4;" k="20" />
    <hkern u1="&#x28;" u2="&#xf3;" k="20" />
    <hkern u1="&#x28;" u2="&#xf2;" k="20" />
    <hkern u1="&#x28;" u2="&#xf1;" k="11" />
    <hkern u1="&#x28;" u2="&#xf0;" k="11" />
    <hkern u1="&#x28;" u2="&#xef;" k="-38" />
    <hkern u1="&#x28;" u2="&#xec;" k="-20" />
    <hkern u1="&#x28;" u2="&#xeb;" k="20" />
    <hkern u1="&#x28;" u2="&#xea;" k="20" />
    <hkern u1="&#x28;" u2="&#xe9;" k="20" />
    <hkern u1="&#x28;" u2="&#xe8;" k="20" />
    <hkern u1="&#x28;" u2="&#xe7;" k="20" />
    <hkern u1="&#x28;" u2="&#xe6;" k="12" />
    <hkern u1="&#x28;" u2="&#xe5;" k="12" />
    <hkern u1="&#x28;" u2="&#xe4;" k="12" />
    <hkern u1="&#x28;" u2="&#xe3;" k="12" />
    <hkern u1="&#x28;" u2="&#xe2;" k="12" />
    <hkern u1="&#x28;" u2="&#xe1;" k="12" />
    <hkern u1="&#x28;" u2="&#xe0;" k="12" />
    <hkern u1="&#x28;" u2="&#xd8;" k="15" />
    <hkern u1="&#x28;" u2="&#xd6;" k="15" />
    <hkern u1="&#x28;" u2="&#xd5;" k="15" />
    <hkern u1="&#x28;" u2="&#xd4;" k="15" />
    <hkern u1="&#x28;" u2="&#xd3;" k="15" />
    <hkern u1="&#x28;" u2="&#xd2;" k="15" />
    <hkern u1="&#x28;" u2="&#xc7;" k="14" />
    <hkern u1="&#x28;" u2="&#x7b;" k="13" />
    <hkern u1="&#x28;" u2="y" k="10" />
    <hkern u1="&#x28;" u2="w" k="14" />
    <hkern u1="&#x28;" u2="v" k="10" />
    <hkern u1="&#x28;" u2="u" k="15" />
    <hkern u1="&#x28;" u2="s" k="10" />
    <hkern u1="&#x28;" u2="r" k="11" />
    <hkern u1="&#x28;" u2="q" k="19" />
    <hkern u1="&#x28;" u2="p" k="11" />
    <hkern u1="&#x28;" u2="o" k="20" />
    <hkern u1="&#x28;" u2="n" k="11" />
    <hkern u1="&#x28;" u2="m" k="11" />
    <hkern u1="&#x28;" u2="j" k="-22" />
    <hkern u1="&#x28;" u2="f" k="10" />
    <hkern u1="&#x28;" u2="e" k="20" />
    <hkern u1="&#x28;" u2="d" k="19" />
    <hkern u1="&#x28;" u2="c" k="20" />
    <hkern u1="&#x28;" u2="a" k="12" />
    <hkern u1="&#x28;" u2="Q" k="15" />
    <hkern u1="&#x28;" u2="O" k="15" />
    <hkern u1="&#x28;" u2="G" k="15" />
    <hkern u1="&#x28;" u2="C" k="14" />
    <hkern u1="&#x2a;" u2="&#x1ef9;" k="-23" />
    <hkern u1="&#x2a;" u2="&#x1ed7;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1ec5;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1eb0;" k="29" />
    <hkern u1="&#x2a;" u2="&#x21a;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x219;" k="11" />
    <hkern u1="&#x2a;" u2="&#x1ff;" k="16" />
    <hkern u1="&#x2a;" u2="&#x1fc;" k="29" />
    <hkern u1="&#x2a;" u2="&#x1fa;" k="29" />
    <hkern u1="&#x2a;" u2="&#x167;" k="-19" />
    <hkern u1="&#x2a;" u2="&#x166;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x165;" k="-18" />
    <hkern u1="&#x2a;" u2="&#x164;" k="-10" />
    <hkern u1="&#x2a;" u2="&#x161;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15f;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15d;" k="11" />
    <hkern u1="&#x2a;" u2="&#x15b;" k="11" />
    <hkern u1="&#x2a;" u2="&#x153;" k="16" />
    <hkern u1="&#x2a;" u2="&#x151;" k="16" />
    <hkern u1="&#x2a;" u2="&#x14f;" k="16" />
    <hkern u1="&#x2a;" u2="&#x14d;" k="16" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-46" />
    <hkern u1="&#x2a;" u2="&#x134;" k="10" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-25" />
    <hkern u1="&#x2a;" u2="&#x123;" k="14" />
    <hkern u1="&#x2a;" u2="&#x121;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11f;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11d;" k="14" />
    <hkern u1="&#x2a;" u2="&#x11b;" k="16" />
    <hkern u1="&#x2a;" u2="&#x119;" k="16" />
    <hkern u1="&#x2a;" u2="&#x117;" k="16" />
    <hkern u1="&#x2a;" u2="&#x115;" k="16" />
    <hkern u1="&#x2a;" u2="&#x113;" k="16" />
    <hkern u1="&#x2a;" u2="&#x111;" k="18" />
    <hkern u1="&#x2a;" u2="&#x10f;" k="18" />
    <hkern u1="&#x2a;" u2="&#x10d;" k="16" />
    <hkern u1="&#x2a;" u2="&#x10b;" k="16" />
    <hkern u1="&#x2a;" u2="&#x109;" k="16" />
    <hkern u1="&#x2a;" u2="&#x107;" k="16" />
    <hkern u1="&#x2a;" u2="&#x104;" k="29" />
    <hkern u1="&#x2a;" u2="&#x102;" k="29" />
    <hkern u1="&#x2a;" u2="&#x100;" k="29" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="16" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="14" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-40" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-53" />
    <hkern u1="&#x2a;" u2="&#xec;" k="-25" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="16" />
    <hkern u1="&#x2a;" u2="&#xea;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="16" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="16" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="36" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="29" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="29" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="29" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="29" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="29" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="29" />
    <hkern u1="&#x2a;" u2="s" k="11" />
    <hkern u1="&#x2a;" u2="q" k="18" />
    <hkern u1="&#x2a;" u2="o" k="16" />
    <hkern u1="&#x2a;" u2="g" k="14" />
    <hkern u1="&#x2a;" u2="e" k="16" />
    <hkern u1="&#x2a;" u2="d" k="18" />
    <hkern u1="&#x2a;" u2="c" k="16" />
    <hkern u1="&#x2a;" u2="T" k="-10" />
    <hkern u1="&#x2a;" u2="J" k="10" />
    <hkern u1="&#x2a;" u2="A" k="29" />
    <hkern u1="&#x2c;" u2="v" k="23" />
    <hkern u1="&#x2c;" u2="f" k="11" />
    <hkern u1="&#x2c;" u2="V" k="40" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="9" />
    <hkern u1="&#x2d;" u2="x" k="23" />
    <hkern u1="&#x2d;" u2="v" k="9" />
    <hkern u1="&#x2d;" u2="f" k="11" />
    <hkern u1="&#x2d;" u2="X" k="32" />
    <hkern u1="&#x2d;" u2="V" k="24" />
    <hkern u1="&#x2e;" u2="v" k="23" />
    <hkern u1="&#x2e;" u2="f" k="11" />
    <hkern u1="&#x2e;" u2="V" k="40" />
    <hkern u1="&#x2f;" u2="&#x1ef9;" k="14" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="14" />
    <hkern u1="&#x2f;" u2="&#x1ed7;" k="36" />
    <hkern u1="&#x2f;" u2="&#x1ec5;" k="36" />
    <hkern u1="&#x2f;" u2="&#x1eb0;" k="37" />
    <hkern u1="&#x2f;" u2="&#x1eab;" k="28" />
    <hkern u1="&#x2f;" u2="&#x1e85;" k="12" />
    <hkern u1="&#x2f;" u2="&#x1e83;" k="12" />
    <hkern u1="&#x2f;" u2="&#x1e81;" k="12" />
    <hkern u1="&#x2f;" u2="&#x219;" k="30" />
    <hkern u1="&#x2f;" u2="&#x1ff;" k="36" />
    <hkern u1="&#x2f;" u2="&#x1fe;" k="15" />
    <hkern u1="&#x2f;" u2="&#x1fd;" k="28" />
    <hkern u1="&#x2f;" u2="&#x1fc;" k="37" />
    <hkern u1="&#x2f;" u2="&#x1fb;" k="28" />
    <hkern u1="&#x2f;" u2="&#x1fa;" k="37" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="17" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="17" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="17" />
    <hkern u1="&#x2f;" u2="&#x177;" k="14" />
    <hkern u1="&#x2f;" u2="&#x175;" k="12" />
    <hkern u1="&#x2f;" u2="&#x173;" k="20" />
    <hkern u1="&#x2f;" u2="&#x171;" k="20" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="20" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="20" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="20" />
    <hkern u1="&#x2f;" u2="&#x169;" k="20" />
    <hkern u1="&#x2f;" u2="&#x161;" k="30" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="30" />
    <hkern u1="&#x2f;" u2="&#x15d;" k="30" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="30" />
    <hkern u1="&#x2f;" u2="&#x159;" k="22" />
    <hkern u1="&#x2f;" u2="&#x157;" k="22" />
    <hkern u1="&#x2f;" u2="&#x155;" k="22" />
    <hkern u1="&#x2f;" u2="&#x153;" k="36" />
    <hkern u1="&#x2f;" u2="&#x152;" k="15" />
    <hkern u1="&#x2f;" u2="&#x151;" k="36" />
    <hkern u1="&#x2f;" u2="&#x150;" k="15" />
    <hkern u1="&#x2f;" u2="&#x14f;" k="36" />
    <hkern u1="&#x2f;" u2="&#x14e;" k="15" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="36" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="15" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="22" />
    <hkern u1="&#x2f;" u2="&#x148;" k="22" />
    <hkern u1="&#x2f;" u2="&#x146;" k="22" />
    <hkern u1="&#x2f;" u2="&#x144;" k="22" />
    <hkern u1="&#x2f;" u2="&#x134;" k="14" />
    <hkern u1="&#x2f;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x2f;" u2="&#x12b;" k="-11" />
    <hkern u1="&#x2f;" u2="&#x129;" k="-30" />
    <hkern u1="&#x2f;" u2="&#x123;" k="34" />
    <hkern u1="&#x2f;" u2="&#x122;" k="15" />
    <hkern u1="&#x2f;" u2="&#x121;" k="34" />
    <hkern u1="&#x2f;" u2="&#x120;" k="15" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="34" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="15" />
    <hkern u1="&#x2f;" u2="&#x11d;" k="34" />
    <hkern u1="&#x2f;" u2="&#x11c;" k="15" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="36" />
    <hkern u1="&#x2f;" u2="&#x119;" k="36" />
    <hkern u1="&#x2f;" u2="&#x117;" k="36" />
    <hkern u1="&#x2f;" u2="&#x115;" k="36" />
    <hkern u1="&#x2f;" u2="&#x113;" k="36" />
    <hkern u1="&#x2f;" u2="&#x111;" k="36" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="36" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="36" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="12" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="36" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="12" />
    <hkern u1="&#x2f;" u2="&#x109;" k="36" />
    <hkern u1="&#x2f;" u2="&#x108;" k="12" />
    <hkern u1="&#x2f;" u2="&#x107;" k="36" />
    <hkern u1="&#x2f;" u2="&#x106;" k="12" />
    <hkern u1="&#x2f;" u2="&#x105;" k="28" />
    <hkern u1="&#x2f;" u2="&#x104;" k="37" />
    <hkern u1="&#x2f;" u2="&#x103;" k="28" />
    <hkern u1="&#x2f;" u2="&#x102;" k="37" />
    <hkern u1="&#x2f;" u2="&#x101;" k="28" />
    <hkern u1="&#x2f;" u2="&#x100;" k="37" />
    <hkern u1="&#x2f;" u2="&#xff;" k="14" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="14" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="20" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="20" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="20" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="20" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="36" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="22" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="10" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-45" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-43" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="36" />
    <hkern u1="&#x2f;" u2="&#xea;" k="36" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="36" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="36" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="36" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="28" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="28" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="15" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="15" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="15" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="15" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="15" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="15" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="12" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="44" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="37" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="37" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="37" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="37" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="37" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="37" />
    <hkern u1="&#x2f;" u2="z" k="17" />
    <hkern u1="&#x2f;" u2="y" k="14" />
    <hkern u1="&#x2f;" u2="w" k="12" />
    <hkern u1="&#x2f;" u2="v" k="13" />
    <hkern u1="&#x2f;" u2="u" k="20" />
    <hkern u1="&#x2f;" u2="s" k="30" />
    <hkern u1="&#x2f;" u2="r" k="22" />
    <hkern u1="&#x2f;" u2="q" k="36" />
    <hkern u1="&#x2f;" u2="p" k="22" />
    <hkern u1="&#x2f;" u2="o" k="36" />
    <hkern u1="&#x2f;" u2="n" k="22" />
    <hkern u1="&#x2f;" u2="m" k="22" />
    <hkern u1="&#x2f;" u2="g" k="34" />
    <hkern u1="&#x2f;" u2="e" k="36" />
    <hkern u1="&#x2f;" u2="d" k="36" />
    <hkern u1="&#x2f;" u2="c" k="36" />
    <hkern u1="&#x2f;" u2="a" k="28" />
    <hkern u1="&#x2f;" u2="Q" k="15" />
    <hkern u1="&#x2f;" u2="O" k="15" />
    <hkern u1="&#x2f;" u2="J" k="14" />
    <hkern u1="&#x2f;" u2="G" k="15" />
    <hkern u1="&#x2f;" u2="C" k="12" />
    <hkern u1="&#x2f;" u2="A" k="37" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="300" />
    <hkern u1="&#x3a;" u2="V" k="10" />
    <hkern u1="&#x3b;" u2="V" k="10" />
    <hkern u1="&#x40;" u2="&#x1ef8;" k="25" />
    <hkern u1="&#x40;" u2="&#x1ef2;" k="25" />
    <hkern u1="&#x40;" u2="&#x178;" k="25" />
    <hkern u1="&#x40;" u2="&#x176;" k="25" />
    <hkern u1="&#x40;" u2="&#xdd;" k="25" />
    <hkern u1="&#x40;" u2="Y" k="25" />
    <hkern u1="A" u2="&#x2122;" k="30" />
    <hkern u1="A" u2="&#xf0;" k="5" />
    <hkern u1="A" u2="&#xae;" k="18" />
    <hkern u1="A" u2="v" k="14" />
    <hkern u1="A" u2="f" k="8" />
    <hkern u1="A" u2="\" k="42" />
    <hkern u1="A" u2="V" k="27" />
    <hkern u1="A" u2="&#x3f;" k="14" />
    <hkern u1="A" u2="&#x2a;" k="27" />
    <hkern u1="B" g2="bracketright.cap" k="13" />
    <hkern u1="B" u2="&#x1ef8;" k="22" />
    <hkern u1="B" u2="&#x1ef2;" k="22" />
    <hkern u1="B" u2="&#x1eb0;" k="8" />
    <hkern u1="B" u2="&#x1fc;" k="8" />
    <hkern u1="B" u2="&#x1fa;" k="8" />
    <hkern u1="B" u2="&#x178;" k="22" />
    <hkern u1="B" u2="&#x176;" k="22" />
    <hkern u1="B" u2="&#x123;" k="7" />
    <hkern u1="B" u2="&#x121;" k="7" />
    <hkern u1="B" u2="&#x11f;" k="7" />
    <hkern u1="B" u2="&#x11d;" k="7" />
    <hkern u1="B" u2="&#x104;" k="8" />
    <hkern u1="B" u2="&#x102;" k="8" />
    <hkern u1="B" u2="&#x100;" k="8" />
    <hkern u1="B" u2="&#xdd;" k="22" />
    <hkern u1="B" u2="&#xc6;" k="11" />
    <hkern u1="B" u2="&#xc5;" k="8" />
    <hkern u1="B" u2="&#xc4;" k="8" />
    <hkern u1="B" u2="&#xc3;" k="8" />
    <hkern u1="B" u2="&#xc2;" k="8" />
    <hkern u1="B" u2="&#xc1;" k="8" />
    <hkern u1="B" u2="&#xc0;" k="8" />
    <hkern u1="B" u2="g" k="7" />
    <hkern u1="B" u2="]" k="14" />
    <hkern u1="B" u2="\" k="16" />
    <hkern u1="B" u2="Y" k="22" />
    <hkern u1="B" u2="X" k="11" />
    <hkern u1="B" u2="V" k="9" />
    <hkern u1="B" u2="A" k="8" />
    <hkern u1="C" u2="&#x135;" k="-21" />
    <hkern u1="C" u2="&#x12d;" k="-10" />
    <hkern u1="C" u2="&#x129;" k="-24" />
    <hkern u1="C" u2="&#xf0;" k="6" />
    <hkern u1="C" u2="&#xef;" k="-35" />
    <hkern u1="C" u2="&#xee;" k="-29" />
    <hkern u1="C" u2="&#xec;" k="-44" />
    <hkern u1="C" u2="v" k="5" />
    <hkern u1="C" u2="f" k="5" />
    <hkern u1="D" g2="braceright.cap" k="13" />
    <hkern u1="D" g2="bracketright.cap" k="20" />
    <hkern u1="D" g2="parenright.cap" k="14" />
    <hkern u1="D" u2="&#xc6;" k="13" />
    <hkern u1="D" u2="&#x7d;" k="12" />
    <hkern u1="D" u2="]" k="19" />
    <hkern u1="D" u2="\" k="18" />
    <hkern u1="D" u2="X" k="18" />
    <hkern u1="D" u2="V" k="10" />
    <hkern u1="D" u2="&#x2f;" k="15" />
    <hkern u1="D" u2="&#x29;" k="13" />
    <hkern u1="E" u2="&#x135;" k="-21" />
    <hkern u1="E" u2="&#x12d;" k="-6" />
    <hkern u1="E" u2="&#x129;" k="-22" />
    <hkern u1="E" u2="&#xf0;" k="6" />
    <hkern u1="E" u2="&#xef;" k="-32" />
    <hkern u1="E" u2="&#xee;" k="-29" />
    <hkern u1="E" u2="&#xec;" k="-42" />
    <hkern u1="F" g2="emdash.cap" k="9" />
    <hkern u1="F" g2="endash.cap" k="9" />
    <hkern u1="F" u2="&#x2026;" k="50" />
    <hkern u1="F" u2="&#x201e;" k="50" />
    <hkern u1="F" u2="&#x201a;" k="50" />
    <hkern u1="F" u2="&#x2014;" k="9" />
    <hkern u1="F" u2="&#x2013;" k="9" />
    <hkern u1="F" u2="&#x1ef9;" k="5" />
    <hkern u1="F" u2="&#x1ef3;" k="5" />
    <hkern u1="F" u2="&#x1ed7;" k="17" />
    <hkern u1="F" u2="&#x1ec5;" k="17" />
    <hkern u1="F" u2="&#x1eb0;" k="21" />
    <hkern u1="F" u2="&#x1eab;" k="17" />
    <hkern u1="F" u2="&#x1e85;" k="6" />
    <hkern u1="F" u2="&#x1e83;" k="6" />
    <hkern u1="F" u2="&#x1e81;" k="6" />
    <hkern u1="F" u2="&#x219;" k="16" />
    <hkern u1="F" u2="&#x1ff;" k="17" />
    <hkern u1="F" u2="&#x1fd;" k="17" />
    <hkern u1="F" u2="&#x1fc;" k="21" />
    <hkern u1="F" u2="&#x1fb;" k="17" />
    <hkern u1="F" u2="&#x1fa;" k="21" />
    <hkern u1="F" u2="&#x17e;" k="10" />
    <hkern u1="F" u2="&#x17c;" k="10" />
    <hkern u1="F" u2="&#x17a;" k="10" />
    <hkern u1="F" u2="&#x177;" k="5" />
    <hkern u1="F" u2="&#x175;" k="6" />
    <hkern u1="F" u2="&#x173;" k="13" />
    <hkern u1="F" u2="&#x171;" k="13" />
    <hkern u1="F" u2="&#x16f;" k="13" />
    <hkern u1="F" u2="&#x16d;" k="13" />
    <hkern u1="F" u2="&#x16b;" k="13" />
    <hkern u1="F" u2="&#x169;" k="13" />
    <hkern u1="F" u2="&#x161;" k="16" />
    <hkern u1="F" u2="&#x15f;" k="16" />
    <hkern u1="F" u2="&#x15d;" k="16" />
    <hkern u1="F" u2="&#x15b;" k="16" />
    <hkern u1="F" u2="&#x159;" k="17" />
    <hkern u1="F" u2="&#x157;" k="17" />
    <hkern u1="F" u2="&#x155;" k="17" />
    <hkern u1="F" u2="&#x153;" k="17" />
    <hkern u1="F" u2="&#x151;" k="17" />
    <hkern u1="F" u2="&#x14f;" k="17" />
    <hkern u1="F" u2="&#x14d;" k="17" />
    <hkern u1="F" u2="&#x14b;" k="17" />
    <hkern u1="F" u2="&#x148;" k="17" />
    <hkern u1="F" u2="&#x146;" k="17" />
    <hkern u1="F" u2="&#x144;" k="17" />
    <hkern u1="F" u2="&#x135;" k="-39" />
    <hkern u1="F" u2="&#x134;" k="8" />
    <hkern u1="F" u2="&#x131;" k="17" />
    <hkern u1="F" u2="&#x12d;" k="-23" />
    <hkern u1="F" u2="&#x12b;" k="-15" />
    <hkern u1="F" u2="&#x129;" k="-39" />
    <hkern u1="F" u2="&#x123;" k="21" />
    <hkern u1="F" u2="&#x121;" k="21" />
    <hkern u1="F" u2="&#x11f;" k="21" />
    <hkern u1="F" u2="&#x11d;" k="21" />
    <hkern u1="F" u2="&#x11b;" k="17" />
    <hkern u1="F" u2="&#x119;" k="17" />
    <hkern u1="F" u2="&#x117;" k="17" />
    <hkern u1="F" u2="&#x115;" k="17" />
    <hkern u1="F" u2="&#x113;" k="17" />
    <hkern u1="F" u2="&#x111;" k="18" />
    <hkern u1="F" u2="&#x10f;" k="18" />
    <hkern u1="F" u2="&#x10d;" k="17" />
    <hkern u1="F" u2="&#x10b;" k="17" />
    <hkern u1="F" u2="&#x109;" k="17" />
    <hkern u1="F" u2="&#x107;" k="17" />
    <hkern u1="F" u2="&#x105;" k="17" />
    <hkern u1="F" u2="&#x104;" k="21" />
    <hkern u1="F" u2="&#x103;" k="17" />
    <hkern u1="F" u2="&#x102;" k="21" />
    <hkern u1="F" u2="&#x101;" k="17" />
    <hkern u1="F" u2="&#x100;" k="21" />
    <hkern u1="F" u2="&#xff;" k="5" />
    <hkern u1="F" u2="&#xfd;" k="5" />
    <hkern u1="F" u2="&#xfc;" k="13" />
    <hkern u1="F" u2="&#xfb;" k="13" />
    <hkern u1="F" u2="&#xfa;" k="13" />
    <hkern u1="F" u2="&#xf9;" k="13" />
    <hkern u1="F" u2="&#xf8;" k="17" />
    <hkern u1="F" u2="&#xf6;" k="17" />
    <hkern u1="F" u2="&#xf5;" k="17" />
    <hkern u1="F" u2="&#xf4;" k="17" />
    <hkern u1="F" u2="&#xf3;" k="17" />
    <hkern u1="F" u2="&#xf2;" k="17" />
    <hkern u1="F" u2="&#xf1;" k="17" />
    <hkern u1="F" u2="&#xf0;" k="12" />
    <hkern u1="F" u2="&#xef;" k="-49" />
    <hkern u1="F" u2="&#xee;" k="-46" />
    <hkern u1="F" u2="&#xec;" k="-59" />
    <hkern u1="F" u2="&#xeb;" k="17" />
    <hkern u1="F" u2="&#xea;" k="17" />
    <hkern u1="F" u2="&#xe9;" k="17" />
    <hkern u1="F" u2="&#xe8;" k="17" />
    <hkern u1="F" u2="&#xe7;" k="17" />
    <hkern u1="F" u2="&#xe6;" k="17" />
    <hkern u1="F" u2="&#xe5;" k="17" />
    <hkern u1="F" u2="&#xe4;" k="17" />
    <hkern u1="F" u2="&#xe3;" k="17" />
    <hkern u1="F" u2="&#xe2;" k="17" />
    <hkern u1="F" u2="&#xe1;" k="17" />
    <hkern u1="F" u2="&#xe0;" k="17" />
    <hkern u1="F" u2="&#xc6;" k="29" />
    <hkern u1="F" u2="&#xc5;" k="21" />
    <hkern u1="F" u2="&#xc4;" k="21" />
    <hkern u1="F" u2="&#xc3;" k="21" />
    <hkern u1="F" u2="&#xc2;" k="21" />
    <hkern u1="F" u2="&#xc1;" k="21" />
    <hkern u1="F" u2="&#xc0;" k="21" />
    <hkern u1="F" u2="z" k="10" />
    <hkern u1="F" u2="y" k="5" />
    <hkern u1="F" u2="x" k="7" />
    <hkern u1="F" u2="w" k="6" />
    <hkern u1="F" u2="u" k="13" />
    <hkern u1="F" u2="s" k="16" />
    <hkern u1="F" u2="r" k="17" />
    <hkern u1="F" u2="q" k="18" />
    <hkern u1="F" u2="p" k="17" />
    <hkern u1="F" u2="o" k="17" />
    <hkern u1="F" u2="n" k="17" />
    <hkern u1="F" u2="m" k="17" />
    <hkern u1="F" u2="g" k="21" />
    <hkern u1="F" u2="f" k="5" />
    <hkern u1="F" u2="e" k="17" />
    <hkern u1="F" u2="d" k="18" />
    <hkern u1="F" u2="c" k="17" />
    <hkern u1="F" u2="a" k="17" />
    <hkern u1="F" u2="J" k="8" />
    <hkern u1="F" u2="A" k="21" />
    <hkern u1="F" u2="&#x2f;" k="38" />
    <hkern u1="F" u2="&#x2e;" k="50" />
    <hkern u1="F" u2="&#x2d;" k="9" />
    <hkern u1="F" u2="&#x2c;" k="50" />
    <hkern u1="G" u2="&#xef;" k="-15" />
    <hkern u1="G" u2="&#xee;" k="-9" />
    <hkern u1="G" u2="&#xec;" k="-25" />
    <hkern u1="G" u2="v" k="5" />
    <hkern u1="G" u2="f" k="6" />
    <hkern u1="G" u2="\" k="10" />
    <hkern u1="G" u2="V" k="8" />
    <hkern u1="H" u2="&#xf0;" k="8" />
    <hkern u1="H" u2="&#xec;" k="-8" />
    <hkern u1="H" u2="f" k="6" />
    <hkern u1="I" u2="&#xf0;" k="8" />
    <hkern u1="I" u2="&#xec;" k="-8" />
    <hkern u1="I" u2="f" k="6" />
    <hkern u1="J" u2="&#xf0;" k="7" />
    <hkern u1="J" u2="&#xec;" k="-11" />
    <hkern u1="J" u2="f" k="5" />
    <hkern u1="K" u2="&#x12d;" k="-25" />
    <hkern u1="K" u2="&#x12b;" k="-17" />
    <hkern u1="K" u2="&#x129;" k="-35" />
    <hkern u1="K" u2="&#xf0;" k="13" />
    <hkern u1="K" u2="&#xef;" k="-51" />
    <hkern u1="K" u2="&#xee;" k="-6" />
    <hkern u1="K" u2="&#xec;" k="-47" />
    <hkern u1="K" u2="v" k="17" />
    <hkern u1="K" u2="f" k="11" />
    <hkern u1="L" g2="periodcentered.cap" k="64" />
    <hkern u1="L" u2="&#x2122;" k="76" />
    <hkern u1="L" u2="&#xae;" k="62" />
    <hkern u1="L" u2="v" k="32" />
    <hkern u1="L" u2="f" k="8" />
    <hkern u1="L" u2="\" k="72" />
    <hkern u1="L" u2="V" k="55" />
    <hkern u1="L" u2="&#x2a;" k="75" />
    <hkern u1="M" u2="&#xf0;" k="8" />
    <hkern u1="M" u2="&#xec;" k="-8" />
    <hkern u1="M" u2="f" k="6" />
    <hkern u1="N" u2="&#xf0;" k="8" />
    <hkern u1="N" u2="&#xec;" k="-8" />
    <hkern u1="N" u2="f" k="6" />
    <hkern u1="O" g2="braceright.cap" k="13" />
    <hkern u1="O" g2="bracketright.cap" k="20" />
    <hkern u1="O" g2="parenright.cap" k="14" />
    <hkern u1="O" u2="&#xc6;" k="13" />
    <hkern u1="O" u2="&#x7d;" k="12" />
    <hkern u1="O" u2="]" k="20" />
    <hkern u1="O" u2="\" k="19" />
    <hkern u1="O" u2="X" k="17" />
    <hkern u1="O" u2="V" k="10" />
    <hkern u1="O" u2="&#x2f;" k="14" />
    <hkern u1="O" u2="&#x29;" k="13" />
    <hkern u1="P" g2="braceright.cap" k="12" />
    <hkern u1="P" g2="bracketright.cap" k="22" />
    <hkern u1="P" g2="parenright.cap" k="13" />
    <hkern u1="P" u2="&#x2026;" k="61" />
    <hkern u1="P" u2="&#x201e;" k="61" />
    <hkern u1="P" u2="&#x201a;" k="61" />
    <hkern u1="P" u2="&#x1ef8;" k="20" />
    <hkern u1="P" u2="&#x1ef2;" k="20" />
    <hkern u1="P" u2="&#x1eb0;" k="19" />
    <hkern u1="P" u2="&#x1eab;" k="5" />
    <hkern u1="P" u2="&#x1fd;" k="5" />
    <hkern u1="P" u2="&#x1fc;" k="19" />
    <hkern u1="P" u2="&#x1fb;" k="5" />
    <hkern u1="P" u2="&#x1fa;" k="19" />
    <hkern u1="P" u2="&#x178;" k="20" />
    <hkern u1="P" u2="&#x176;" k="20" />
    <hkern u1="P" u2="&#x135;" k="-8" />
    <hkern u1="P" u2="&#x134;" k="9" />
    <hkern u1="P" u2="&#x105;" k="5" />
    <hkern u1="P" u2="&#x104;" k="19" />
    <hkern u1="P" u2="&#x103;" k="5" />
    <hkern u1="P" u2="&#x102;" k="19" />
    <hkern u1="P" u2="&#x101;" k="5" />
    <hkern u1="P" u2="&#x100;" k="19" />
    <hkern u1="P" u2="&#xf0;" k="12" />
    <hkern u1="P" u2="&#xef;" k="-8" />
    <hkern u1="P" u2="&#xee;" k="-16" />
    <hkern u1="P" u2="&#xec;" k="-11" />
    <hkern u1="P" u2="&#xe6;" k="5" />
    <hkern u1="P" u2="&#xe5;" k="5" />
    <hkern u1="P" u2="&#xe4;" k="5" />
    <hkern u1="P" u2="&#xe3;" k="5" />
    <hkern u1="P" u2="&#xe2;" k="5" />
    <hkern u1="P" u2="&#xe1;" k="5" />
    <hkern u1="P" u2="&#xe0;" k="5" />
    <hkern u1="P" u2="&#xdd;" k="20" />
    <hkern u1="P" u2="&#xc6;" k="25" />
    <hkern u1="P" u2="&#xc5;" k="19" />
    <hkern u1="P" u2="&#xc4;" k="19" />
    <hkern u1="P" u2="&#xc3;" k="19" />
    <hkern u1="P" u2="&#xc2;" k="19" />
    <hkern u1="P" u2="&#xc1;" k="19" />
    <hkern u1="P" u2="&#xc0;" k="19" />
    <hkern u1="P" u2="&#x7d;" k="10" />
    <hkern u1="P" u2="a" k="5" />
    <hkern u1="P" u2="]" k="13" />
    <hkern u1="P" u2="\" k="12" />
    <hkern u1="P" u2="Y" k="20" />
    <hkern u1="P" u2="X" k="17" />
    <hkern u1="P" u2="V" k="7" />
    <hkern u1="P" u2="J" k="9" />
    <hkern u1="P" u2="A" k="19" />
    <hkern u1="P" u2="&#x2f;" k="38" />
    <hkern u1="P" u2="&#x2e;" k="61" />
    <hkern u1="P" u2="&#x2c;" k="61" />
    <hkern u1="P" u2="&#x29;" k="10" />
    <hkern u1="Q" g2="braceright.cap" k="13" />
    <hkern u1="Q" g2="bracketright.cap" k="20" />
    <hkern u1="Q" g2="parenright.cap" k="14" />
    <hkern u1="Q" u2="&#xc6;" k="13" />
    <hkern u1="Q" u2="&#x7d;" k="12" />
    <hkern u1="Q" u2="]" k="20" />
    <hkern u1="Q" u2="\" k="19" />
    <hkern u1="Q" u2="X" k="17" />
    <hkern u1="Q" u2="V" k="10" />
    <hkern u1="Q" u2="&#x2f;" k="14" />
    <hkern u1="Q" u2="&#x29;" k="13" />
    <hkern u1="R" u2="&#xf0;" k="14" />
    <hkern u1="R" u2="&#xc6;" k="10" />
    <hkern u1="R" u2="\" k="15" />
    <hkern u1="R" u2="X" k="6" />
    <hkern u1="R" u2="V" k="9" />
    <hkern u1="S" u2="&#x129;" k="-10" />
    <hkern u1="S" u2="&#xef;" k="-20" />
    <hkern u1="S" u2="&#xee;" k="-8" />
    <hkern u1="S" u2="&#xec;" k="-28" />
    <hkern u1="S" u2="&#xc6;" k="10" />
    <hkern u1="S" u2="x" k="8" />
    <hkern u1="S" u2="v" k="6" />
    <hkern u1="S" u2="f" k="9" />
    <hkern u1="S" u2="X" k="5" />
    <hkern u1="S" u2="V" k="8" />
    <hkern u1="T" u2="&#x1ef9;" k="50" />
    <hkern u1="T" u2="&#x1eab;" k="50" />
    <hkern u1="T" u2="&#x16d;" k="54" />
    <hkern u1="T" u2="&#x169;" k="54" />
    <hkern u1="T" u2="&#x15d;" k="57" />
    <hkern u1="T" u2="&#x159;" k="37" />
    <hkern u1="T" u2="&#x155;" k="38" />
    <hkern u1="T" u2="&#x151;" k="42" />
    <hkern u1="T" u2="&#x135;" k="-48" />
    <hkern u1="T" u2="&#x131;" k="52" />
    <hkern u1="T" u2="&#x12d;" k="-32" />
    <hkern u1="T" u2="&#x12b;" k="-24" />
    <hkern u1="T" u2="&#x129;" k="-48" />
    <hkern u1="T" u2="&#x11f;" k="75" />
    <hkern u1="T" u2="&#x109;" k="43" />
    <hkern u1="T" u2="&#xf0;" k="18" />
    <hkern u1="T" u2="&#xef;" k="-58" />
    <hkern u1="T" u2="&#xee;" k="-55" />
    <hkern u1="T" u2="&#xec;" k="-68" />
    <hkern u1="T" u2="&#xe4;" k="54" />
    <hkern u1="T" u2="&#xe3;" k="44" />
    <hkern u1="T" u2="&#xc6;" k="49" />
    <hkern u1="T" u2="x" k="49" />
    <hkern u1="T" u2="v" k="49" />
    <hkern u1="T" u2="f" k="15" />
    <hkern u1="T" u2="&#x40;" k="15" />
    <hkern u1="T" u2="&#x2f;" k="49" />
    <hkern u1="T" u2="&#x26;" k="10" />
    <hkern u1="U" u2="&#xf0;" k="8" />
    <hkern u1="U" u2="&#xec;" k="-14" />
    <hkern u1="U" u2="&#xc6;" k="7" />
    <hkern u1="U" u2="f" k="5" />
    <hkern u1="U" u2="&#x2f;" k="16" />
    <hkern u1="V" g2="emdash.cap" k="21" />
    <hkern u1="V" g2="endash.cap" k="21" />
    <hkern u1="V" u2="&#x203a;" k="11" />
    <hkern u1="V" u2="&#x2039;" k="24" />
    <hkern u1="V" u2="&#x2026;" k="40" />
    <hkern u1="V" u2="&#x201e;" k="40" />
    <hkern u1="V" u2="&#x201a;" k="40" />
    <hkern u1="V" u2="&#x2014;" k="24" />
    <hkern u1="V" u2="&#x2013;" k="24" />
    <hkern u1="V" u2="&#x1ef9;" k="6" />
    <hkern u1="V" u2="&#x1ef3;" k="6" />
    <hkern u1="V" u2="&#x1ed7;" k="29" />
    <hkern u1="V" u2="&#x1ec5;" k="29" />
    <hkern u1="V" u2="&#x1eb0;" k="27" />
    <hkern u1="V" u2="&#x1eab;" k="23" />
    <hkern u1="V" u2="&#x1e85;" k="9" />
    <hkern u1="V" u2="&#x1e83;" k="9" />
    <hkern u1="V" u2="&#x1e81;" k="9" />
    <hkern u1="V" u2="&#x219;" k="22" />
    <hkern u1="V" u2="&#x218;" k="8" />
    <hkern u1="V" u2="&#x1ff;" k="29" />
    <hkern u1="V" u2="&#x1fe;" k="10" />
    <hkern u1="V" u2="&#x1fd;" k="23" />
    <hkern u1="V" u2="&#x1fc;" k="27" />
    <hkern u1="V" u2="&#x1fb;" k="23" />
    <hkern u1="V" u2="&#x1fa;" k="27" />
    <hkern u1="V" u2="&#x17e;" k="13" />
    <hkern u1="V" u2="&#x17c;" k="13" />
    <hkern u1="V" u2="&#x17a;" k="13" />
    <hkern u1="V" u2="&#x177;" k="6" />
    <hkern u1="V" u2="&#x175;" k="9" />
    <hkern u1="V" u2="&#x173;" k="17" />
    <hkern u1="V" u2="&#x171;" k="17" />
    <hkern u1="V" u2="&#x16f;" k="17" />
    <hkern u1="V" u2="&#x16d;" k="17" />
    <hkern u1="V" u2="&#x16b;" k="17" />
    <hkern u1="V" u2="&#x169;" k="17" />
    <hkern u1="V" u2="&#x161;" k="22" />
    <hkern u1="V" u2="&#x160;" k="8" />
    <hkern u1="V" u2="&#x15f;" k="22" />
    <hkern u1="V" u2="&#x15e;" k="8" />
    <hkern u1="V" u2="&#x15d;" k="22" />
    <hkern u1="V" u2="&#x15c;" k="8" />
    <hkern u1="V" u2="&#x15b;" k="22" />
    <hkern u1="V" u2="&#x15a;" k="8" />
    <hkern u1="V" u2="&#x159;" k="21" />
    <hkern u1="V" u2="&#x157;" k="21" />
    <hkern u1="V" u2="&#x155;" k="21" />
    <hkern u1="V" u2="&#x153;" k="29" />
    <hkern u1="V" u2="&#x152;" k="10" />
    <hkern u1="V" u2="&#x151;" k="29" />
    <hkern u1="V" u2="&#x150;" k="10" />
    <hkern u1="V" u2="&#x14f;" k="29" />
    <hkern u1="V" u2="&#x14e;" k="10" />
    <hkern u1="V" u2="&#x14d;" k="29" />
    <hkern u1="V" u2="&#x14c;" k="10" />
    <hkern u1="V" u2="&#x14b;" k="21" />
    <hkern u1="V" u2="&#x148;" k="21" />
    <hkern u1="V" u2="&#x146;" k="21" />
    <hkern u1="V" u2="&#x144;" k="21" />
    <hkern u1="V" u2="&#x135;" k="-23" />
    <hkern u1="V" u2="&#x134;" k="11" />
    <hkern u1="V" u2="&#x131;" k="21" />
    <hkern u1="V" u2="&#x12d;" k="-30" />
    <hkern u1="V" u2="&#x12b;" k="-22" />
    <hkern u1="V" u2="&#x129;" k="-42" />
    <hkern u1="V" u2="&#x123;" k="30" />
    <hkern u1="V" u2="&#x122;" k="10" />
    <hkern u1="V" u2="&#x121;" k="30" />
    <hkern u1="V" u2="&#x120;" k="10" />
    <hkern u1="V" u2="&#x11f;" k="30" />
    <hkern u1="V" u2="&#x11e;" k="10" />
    <hkern u1="V" u2="&#x11d;" k="30" />
    <hkern u1="V" u2="&#x11c;" k="10" />
    <hkern u1="V" u2="&#x11b;" k="29" />
    <hkern u1="V" u2="&#x119;" k="29" />
    <hkern u1="V" u2="&#x117;" k="29" />
    <hkern u1="V" u2="&#x115;" k="29" />
    <hkern u1="V" u2="&#x113;" k="29" />
    <hkern u1="V" u2="&#x111;" k="28" />
    <hkern u1="V" u2="&#x10f;" k="28" />
    <hkern u1="V" u2="&#x10d;" k="29" />
    <hkern u1="V" u2="&#x10c;" k="9" />
    <hkern u1="V" u2="&#x10b;" k="29" />
    <hkern u1="V" u2="&#x10a;" k="9" />
    <hkern u1="V" u2="&#x109;" k="29" />
    <hkern u1="V" u2="&#x108;" k="9" />
    <hkern u1="V" u2="&#x107;" k="29" />
    <hkern u1="V" u2="&#x106;" k="9" />
    <hkern u1="V" u2="&#x105;" k="23" />
    <hkern u1="V" u2="&#x104;" k="27" />
    <hkern u1="V" u2="&#x103;" k="23" />
    <hkern u1="V" u2="&#x102;" k="27" />
    <hkern u1="V" u2="&#x101;" k="23" />
    <hkern u1="V" u2="&#x100;" k="27" />
    <hkern u1="V" u2="&#xff;" k="6" />
    <hkern u1="V" u2="&#xfd;" k="6" />
    <hkern u1="V" u2="&#xfc;" k="17" />
    <hkern u1="V" u2="&#xfb;" k="17" />
    <hkern u1="V" u2="&#xfa;" k="17" />
    <hkern u1="V" u2="&#xf9;" k="17" />
    <hkern u1="V" u2="&#xf8;" k="29" />
    <hkern u1="V" u2="&#xf6;" k="29" />
    <hkern u1="V" u2="&#xf5;" k="29" />
    <hkern u1="V" u2="&#xf4;" k="29" />
    <hkern u1="V" u2="&#xf3;" k="29" />
    <hkern u1="V" u2="&#xf2;" k="29" />
    <hkern u1="V" u2="&#xf1;" k="21" />
    <hkern u1="V" u2="&#xf0;" k="19" />
    <hkern u1="V" u2="&#xef;" k="-56" />
    <hkern u1="V" u2="&#xee;" k="-30" />
    <hkern u1="V" u2="&#xec;" k="-57" />
    <hkern u1="V" u2="&#xeb;" k="29" />
    <hkern u1="V" u2="&#xea;" k="29" />
    <hkern u1="V" u2="&#xe9;" k="29" />
    <hkern u1="V" u2="&#xe8;" k="29" />
    <hkern u1="V" u2="&#xe7;" k="29" />
    <hkern u1="V" u2="&#xe6;" k="23" />
    <hkern u1="V" u2="&#xe5;" k="23" />
    <hkern u1="V" u2="&#xe4;" k="23" />
    <hkern u1="V" u2="&#xe3;" k="23" />
    <hkern u1="V" u2="&#xe2;" k="23" />
    <hkern u1="V" u2="&#xe1;" k="23" />
    <hkern u1="V" u2="&#xe0;" k="23" />
    <hkern u1="V" u2="&#xd8;" k="10" />
    <hkern u1="V" u2="&#xd6;" k="10" />
    <hkern u1="V" u2="&#xd5;" k="10" />
    <hkern u1="V" u2="&#xd4;" k="10" />
    <hkern u1="V" u2="&#xd3;" k="10" />
    <hkern u1="V" u2="&#xd2;" k="10" />
    <hkern u1="V" u2="&#xc7;" k="9" />
    <hkern u1="V" u2="&#xc6;" k="31" />
    <hkern u1="V" u2="&#xc5;" k="27" />
    <hkern u1="V" u2="&#xc4;" k="27" />
    <hkern u1="V" u2="&#xc3;" k="27" />
    <hkern u1="V" u2="&#xc2;" k="27" />
    <hkern u1="V" u2="&#xc1;" k="27" />
    <hkern u1="V" u2="&#xc0;" k="27" />
    <hkern u1="V" u2="&#xbb;" k="11" />
    <hkern u1="V" u2="&#xab;" k="24" />
    <hkern u1="V" u2="z" k="13" />
    <hkern u1="V" u2="y" k="6" />
    <hkern u1="V" u2="x" k="7" />
    <hkern u1="V" u2="w" k="9" />
    <hkern u1="V" u2="v" k="6" />
    <hkern u1="V" u2="u" k="17" />
    <hkern u1="V" u2="s" k="22" />
    <hkern u1="V" u2="r" k="21" />
    <hkern u1="V" u2="q" k="28" />
    <hkern u1="V" u2="p" k="21" />
    <hkern u1="V" u2="o" k="29" />
    <hkern u1="V" u2="n" k="21" />
    <hkern u1="V" u2="m" k="21" />
    <hkern u1="V" u2="g" k="30" />
    <hkern u1="V" u2="f" k="7" />
    <hkern u1="V" u2="e" k="29" />
    <hkern u1="V" u2="d" k="28" />
    <hkern u1="V" u2="c" k="29" />
    <hkern u1="V" u2="a" k="23" />
    <hkern u1="V" u2="S" k="8" />
    <hkern u1="V" u2="Q" k="10" />
    <hkern u1="V" u2="O" k="10" />
    <hkern u1="V" u2="J" k="11" />
    <hkern u1="V" u2="G" k="10" />
    <hkern u1="V" u2="C" k="9" />
    <hkern u1="V" u2="A" k="27" />
    <hkern u1="V" u2="&#x40;" k="12" />
    <hkern u1="V" u2="&#x3b;" k="10" />
    <hkern u1="V" u2="&#x3a;" k="10" />
    <hkern u1="V" u2="&#x2f;" k="40" />
    <hkern u1="V" u2="&#x2e;" k="40" />
    <hkern u1="V" u2="&#x2d;" k="24" />
    <hkern u1="V" u2="&#x2c;" k="40" />
    <hkern u1="V" u2="&#x26;" k="14" />
    <hkern u1="W" u2="&#x135;" k="-24" />
    <hkern u1="W" u2="&#x131;" k="13" />
    <hkern u1="W" u2="&#x12d;" k="-26" />
    <hkern u1="W" u2="&#x12b;" k="-18" />
    <hkern u1="W" u2="&#x129;" k="-39" />
    <hkern u1="W" u2="&#xf0;" k="13" />
    <hkern u1="W" u2="&#xef;" k="-53" />
    <hkern u1="W" u2="&#xee;" k="-30" />
    <hkern u1="W" u2="&#xec;" k="-54" />
    <hkern u1="W" u2="&#xc6;" k="23" />
    <hkern u1="W" u2="&#x2f;" k="29" />
    <hkern u1="X" g2="emdash.cap" k="38" />
    <hkern u1="X" g2="endash.cap" k="38" />
    <hkern u1="X" u2="&#x2039;" k="18" />
    <hkern u1="X" u2="&#x2014;" k="32" />
    <hkern u1="X" u2="&#x2013;" k="32" />
    <hkern u1="X" u2="&#x1ef9;" k="23" />
    <hkern u1="X" u2="&#x1ef3;" k="23" />
    <hkern u1="X" u2="&#x1ed7;" k="26" />
    <hkern u1="X" u2="&#x1ec5;" k="26" />
    <hkern u1="X" u2="&#x1eab;" k="5" />
    <hkern u1="X" u2="&#x1e85;" k="24" />
    <hkern u1="X" u2="&#x1e83;" k="24" />
    <hkern u1="X" u2="&#x1e81;" k="24" />
    <hkern u1="X" u2="&#x21b;" k="10" />
    <hkern u1="X" u2="&#x1ff;" k="26" />
    <hkern u1="X" u2="&#x1fe;" k="17" />
    <hkern u1="X" u2="&#x1fd;" k="5" />
    <hkern u1="X" u2="&#x1fb;" k="5" />
    <hkern u1="X" u2="&#x177;" k="23" />
    <hkern u1="X" u2="&#x175;" k="24" />
    <hkern u1="X" u2="&#x173;" k="19" />
    <hkern u1="X" u2="&#x171;" k="19" />
    <hkern u1="X" u2="&#x16f;" k="19" />
    <hkern u1="X" u2="&#x16d;" k="19" />
    <hkern u1="X" u2="&#x16b;" k="19" />
    <hkern u1="X" u2="&#x169;" k="19" />
    <hkern u1="X" u2="&#x167;" k="10" />
    <hkern u1="X" u2="&#x165;" k="10" />
    <hkern u1="X" u2="&#x159;" k="5" />
    <hkern u1="X" u2="&#x157;" k="5" />
    <hkern u1="X" u2="&#x155;" k="5" />
    <hkern u1="X" u2="&#x153;" k="26" />
    <hkern u1="X" u2="&#x152;" k="17" />
    <hkern u1="X" u2="&#x151;" k="26" />
    <hkern u1="X" u2="&#x150;" k="17" />
    <hkern u1="X" u2="&#x14f;" k="26" />
    <hkern u1="X" u2="&#x14e;" k="17" />
    <hkern u1="X" u2="&#x14d;" k="26" />
    <hkern u1="X" u2="&#x14c;" k="17" />
    <hkern u1="X" u2="&#x14b;" k="5" />
    <hkern u1="X" u2="&#x148;" k="5" />
    <hkern u1="X" u2="&#x146;" k="5" />
    <hkern u1="X" u2="&#x144;" k="5" />
    <hkern u1="X" u2="&#x135;" k="-7" />
    <hkern u1="X" u2="&#x12d;" k="-36" />
    <hkern u1="X" u2="&#x12b;" k="-28" />
    <hkern u1="X" u2="&#x129;" k="-42" />
    <hkern u1="X" u2="&#x123;" k="20" />
    <hkern u1="X" u2="&#x122;" k="17" />
    <hkern u1="X" u2="&#x121;" k="20" />
    <hkern u1="X" u2="&#x120;" k="17" />
    <hkern u1="X" u2="&#x11f;" k="20" />
    <hkern u1="X" u2="&#x11e;" k="17" />
    <hkern u1="X" u2="&#x11d;" k="20" />
    <hkern u1="X" u2="&#x11c;" k="17" />
    <hkern u1="X" u2="&#x11b;" k="26" />
    <hkern u1="X" u2="&#x119;" k="26" />
    <hkern u1="X" u2="&#x117;" k="26" />
    <hkern u1="X" u2="&#x115;" k="26" />
    <hkern u1="X" u2="&#x113;" k="26" />
    <hkern u1="X" u2="&#x111;" k="20" />
    <hkern u1="X" u2="&#x10f;" k="20" />
    <hkern u1="X" u2="&#x10d;" k="26" />
    <hkern u1="X" u2="&#x10c;" k="16" />
    <hkern u1="X" u2="&#x10b;" k="26" />
    <hkern u1="X" u2="&#x10a;" k="16" />
    <hkern u1="X" u2="&#x109;" k="26" />
    <hkern u1="X" u2="&#x108;" k="16" />
    <hkern u1="X" u2="&#x107;" k="26" />
    <hkern u1="X" u2="&#x106;" k="16" />
    <hkern u1="X" u2="&#x105;" k="5" />
    <hkern u1="X" u2="&#x103;" k="5" />
    <hkern u1="X" u2="&#x101;" k="5" />
    <hkern u1="X" u2="&#xff;" k="23" />
    <hkern u1="X" u2="&#xfd;" k="23" />
    <hkern u1="X" u2="&#xfc;" k="19" />
    <hkern u1="X" u2="&#xfb;" k="19" />
    <hkern u1="X" u2="&#xfa;" k="19" />
    <hkern u1="X" u2="&#xf9;" k="19" />
    <hkern u1="X" u2="&#xf8;" k="26" />
    <hkern u1="X" u2="&#xf6;" k="26" />
    <hkern u1="X" u2="&#xf5;" k="26" />
    <hkern u1="X" u2="&#xf4;" k="26" />
    <hkern u1="X" u2="&#xf3;" k="26" />
    <hkern u1="X" u2="&#xf2;" k="26" />
    <hkern u1="X" u2="&#xf1;" k="5" />
    <hkern u1="X" u2="&#xf0;" k="17" />
    <hkern u1="X" u2="&#xef;" k="-62" />
    <hkern u1="X" u2="&#xee;" k="-13" />
    <hkern u1="X" u2="&#xec;" k="-55" />
    <hkern u1="X" u2="&#xeb;" k="26" />
    <hkern u1="X" u2="&#xea;" k="26" />
    <hkern u1="X" u2="&#xe9;" k="26" />
    <hkern u1="X" u2="&#xe8;" k="26" />
    <hkern u1="X" u2="&#xe7;" k="26" />
    <hkern u1="X" u2="&#xe6;" k="5" />
    <hkern u1="X" u2="&#xe5;" k="5" />
    <hkern u1="X" u2="&#xe4;" k="5" />
    <hkern u1="X" u2="&#xe3;" k="5" />
    <hkern u1="X" u2="&#xe2;" k="5" />
    <hkern u1="X" u2="&#xe1;" k="5" />
    <hkern u1="X" u2="&#xe0;" k="5" />
    <hkern u1="X" u2="&#xd8;" k="17" />
    <hkern u1="X" u2="&#xd6;" k="17" />
    <hkern u1="X" u2="&#xd5;" k="17" />
    <hkern u1="X" u2="&#xd4;" k="17" />
    <hkern u1="X" u2="&#xd3;" k="17" />
    <hkern u1="X" u2="&#xd2;" k="17" />
    <hkern u1="X" u2="&#xc7;" k="16" />
    <hkern u1="X" u2="&#xab;" k="18" />
    <hkern u1="X" u2="y" k="23" />
    <hkern u1="X" u2="w" k="24" />
    <hkern u1="X" u2="v" k="22" />
    <hkern u1="X" u2="u" k="19" />
    <hkern u1="X" u2="t" k="10" />
    <hkern u1="X" u2="r" k="5" />
    <hkern u1="X" u2="q" k="20" />
    <hkern u1="X" u2="p" k="5" />
    <hkern u1="X" u2="o" k="26" />
    <hkern u1="X" u2="n" k="5" />
    <hkern u1="X" u2="m" k="5" />
    <hkern u1="X" u2="g" k="20" />
    <hkern u1="X" u2="f" k="9" />
    <hkern u1="X" u2="e" k="26" />
    <hkern u1="X" u2="d" k="20" />
    <hkern u1="X" u2="c" k="26" />
    <hkern u1="X" u2="a" k="5" />
    <hkern u1="X" u2="Q" k="17" />
    <hkern u1="X" u2="O" k="17" />
    <hkern u1="X" u2="G" k="17" />
    <hkern u1="X" u2="C" k="16" />
    <hkern u1="X" u2="&#x2d;" k="32" />
    <hkern u1="Y" u2="&#x1ef9;" k="24" />
    <hkern u1="Y" u2="&#x1ef3;" k="26" />
    <hkern u1="Y" u2="&#x159;" k="26" />
    <hkern u1="Y" u2="&#x155;" k="32" />
    <hkern u1="Y" u2="&#x151;" k="48" />
    <hkern u1="Y" u2="&#x142;" k="6" />
    <hkern u1="Y" u2="&#x135;" k="-15" />
    <hkern u1="Y" u2="&#x131;" k="59" />
    <hkern u1="Y" u2="&#x12d;" k="-44" />
    <hkern u1="Y" u2="&#x12b;" k="-36" />
    <hkern u1="Y" u2="&#x129;" k="-49" />
    <hkern u1="Y" u2="&#x103;" k="56" />
    <hkern u1="Y" u2="&#xff;" k="24" />
    <hkern u1="Y" u2="&#xf0;" k="26" />
    <hkern u1="Y" u2="&#xef;" k="-70" />
    <hkern u1="Y" u2="&#xee;" k="-21" />
    <hkern u1="Y" u2="&#xec;" k="-62" />
    <hkern u1="Y" u2="&#xeb;" k="61" />
    <hkern u1="Y" u2="&#xe4;" k="43" />
    <hkern u1="Y" u2="&#xe3;" k="34" />
    <hkern u1="Y" u2="&#xdf;" k="14" />
    <hkern u1="Y" u2="&#xc6;" k="59" />
    <hkern u1="Y" u2="&#xae;" k="19" />
    <hkern u1="Y" u2="x" k="34" />
    <hkern u1="Y" u2="v" k="33" />
    <hkern u1="Y" u2="f" k="23" />
    <hkern u1="Y" u2="&#x40;" k="35" />
    <hkern u1="Y" u2="&#x2f;" k="65" />
    <hkern u1="Y" u2="&#x2a;" k="-5" />
    <hkern u1="Y" u2="&#x26;" k="28" />
    <hkern u1="Z" u2="&#x135;" k="-20" />
    <hkern u1="Z" u2="&#x129;" k="-20" />
    <hkern u1="Z" u2="&#xf0;" k="7" />
    <hkern u1="Z" u2="&#xef;" k="-30" />
    <hkern u1="Z" u2="&#xee;" k="-28" />
    <hkern u1="Z" u2="&#xec;" k="-41" />
    <hkern u1="Z" u2="v" k="7" />
    <hkern u1="Z" u2="f" k="7" />
    <hkern u1="[" u2="&#x1ef9;" k="20" />
    <hkern u1="[" u2="&#x1ef3;" k="20" />
    <hkern u1="[" u2="&#x1ed7;" k="27" />
    <hkern u1="[" u2="&#x1ec5;" k="27" />
    <hkern u1="[" u2="&#x1eab;" k="17" />
    <hkern u1="[" u2="&#x1e85;" k="21" />
    <hkern u1="[" u2="&#x1e83;" k="21" />
    <hkern u1="[" u2="&#x1e81;" k="21" />
    <hkern u1="[" u2="&#x21b;" k="11" />
    <hkern u1="[" u2="&#x1ff;" k="27" />
    <hkern u1="[" u2="&#x1fe;" k="20" />
    <hkern u1="[" u2="&#x1fd;" k="17" />
    <hkern u1="[" u2="&#x1fb;" k="17" />
    <hkern u1="[" u2="&#x177;" k="20" />
    <hkern u1="[" u2="&#x175;" k="21" />
    <hkern u1="[" u2="&#x173;" k="20" />
    <hkern u1="[" u2="&#x171;" k="20" />
    <hkern u1="[" u2="&#x16f;" k="20" />
    <hkern u1="[" u2="&#x16d;" k="20" />
    <hkern u1="[" u2="&#x16b;" k="20" />
    <hkern u1="[" u2="&#x169;" k="20" />
    <hkern u1="[" u2="&#x167;" k="11" />
    <hkern u1="[" u2="&#x165;" k="11" />
    <hkern u1="[" u2="&#x153;" k="27" />
    <hkern u1="[" u2="&#x152;" k="20" />
    <hkern u1="[" u2="&#x151;" k="27" />
    <hkern u1="[" u2="&#x150;" k="20" />
    <hkern u1="[" u2="&#x14f;" k="27" />
    <hkern u1="[" u2="&#x14e;" k="20" />
    <hkern u1="[" u2="&#x14d;" k="27" />
    <hkern u1="[" u2="&#x14c;" k="20" />
    <hkern u1="[" u2="&#x135;" k="-9" />
    <hkern u1="[" u2="&#x12d;" k="-25" />
    <hkern u1="[" u2="&#x129;" k="-28" />
    <hkern u1="[" u2="&#x122;" k="20" />
    <hkern u1="[" u2="&#x120;" k="20" />
    <hkern u1="[" u2="&#x11e;" k="20" />
    <hkern u1="[" u2="&#x11c;" k="20" />
    <hkern u1="[" u2="&#x11b;" k="27" />
    <hkern u1="[" u2="&#x119;" k="27" />
    <hkern u1="[" u2="&#x117;" k="27" />
    <hkern u1="[" u2="&#x115;" k="27" />
    <hkern u1="[" u2="&#x113;" k="27" />
    <hkern u1="[" u2="&#x111;" k="26" />
    <hkern u1="[" u2="&#x10f;" k="26" />
    <hkern u1="[" u2="&#x10d;" k="27" />
    <hkern u1="[" u2="&#x10c;" k="16" />
    <hkern u1="[" u2="&#x10b;" k="27" />
    <hkern u1="[" u2="&#x10a;" k="16" />
    <hkern u1="[" u2="&#x109;" k="27" />
    <hkern u1="[" u2="&#x108;" k="16" />
    <hkern u1="[" u2="&#x107;" k="27" />
    <hkern u1="[" u2="&#x106;" k="16" />
    <hkern u1="[" u2="&#x105;" k="17" />
    <hkern u1="[" u2="&#x103;" k="17" />
    <hkern u1="[" u2="&#x101;" k="17" />
    <hkern u1="[" u2="&#xff;" k="20" />
    <hkern u1="[" u2="&#xfd;" k="20" />
    <hkern u1="[" u2="&#xfc;" k="20" />
    <hkern u1="[" u2="&#xfb;" k="20" />
    <hkern u1="[" u2="&#xfa;" k="20" />
    <hkern u1="[" u2="&#xf9;" k="20" />
    <hkern u1="[" u2="&#xf8;" k="27" />
    <hkern u1="[" u2="&#xf6;" k="27" />
    <hkern u1="[" u2="&#xf5;" k="27" />
    <hkern u1="[" u2="&#xf4;" k="27" />
    <hkern u1="[" u2="&#xf3;" k="27" />
    <hkern u1="[" u2="&#xf2;" k="27" />
    <hkern u1="[" u2="&#xf0;" k="11" />
    <hkern u1="[" u2="&#xef;" k="-38" />
    <hkern u1="[" u2="&#xec;" k="-48" />
    <hkern u1="[" u2="&#xeb;" k="27" />
    <hkern u1="[" u2="&#xea;" k="27" />
    <hkern u1="[" u2="&#xe9;" k="27" />
    <hkern u1="[" u2="&#xe8;" k="27" />
    <hkern u1="[" u2="&#xe7;" k="27" />
    <hkern u1="[" u2="&#xe6;" k="17" />
    <hkern u1="[" u2="&#xe5;" k="17" />
    <hkern u1="[" u2="&#xe4;" k="17" />
    <hkern u1="[" u2="&#xe3;" k="17" />
    <hkern u1="[" u2="&#xe2;" k="17" />
    <hkern u1="[" u2="&#xe1;" k="17" />
    <hkern u1="[" u2="&#xe0;" k="17" />
    <hkern u1="[" u2="&#xd8;" k="20" />
    <hkern u1="[" u2="&#xd6;" k="20" />
    <hkern u1="[" u2="&#xd5;" k="20" />
    <hkern u1="[" u2="&#xd4;" k="20" />
    <hkern u1="[" u2="&#xd3;" k="20" />
    <hkern u1="[" u2="&#xd2;" k="20" />
    <hkern u1="[" u2="&#xc7;" k="16" />
    <hkern u1="[" u2="&#x7b;" k="18" />
    <hkern u1="[" u2="y" k="20" />
    <hkern u1="[" u2="w" k="21" />
    <hkern u1="[" u2="v" k="20" />
    <hkern u1="[" u2="u" k="20" />
    <hkern u1="[" u2="t" k="11" />
    <hkern u1="[" u2="q" k="26" />
    <hkern u1="[" u2="o" k="27" />
    <hkern u1="[" u2="j" k="-9" />
    <hkern u1="[" u2="f" k="10" />
    <hkern u1="[" u2="e" k="27" />
    <hkern u1="[" u2="d" k="26" />
    <hkern u1="[" u2="c" k="27" />
    <hkern u1="[" u2="a" k="17" />
    <hkern u1="[" u2="Q" k="20" />
    <hkern u1="[" u2="O" k="20" />
    <hkern u1="[" u2="G" k="20" />
    <hkern u1="[" u2="C" k="16" />
    <hkern u1="\" u2="&#x201d;" k="68" />
    <hkern u1="\" u2="&#x2019;" k="68" />
    <hkern u1="\" u2="&#x1ef9;" k="22" />
    <hkern u1="\" u2="&#x1ef8;" k="71" />
    <hkern u1="\" u2="&#x1ef3;" k="22" />
    <hkern u1="\" u2="&#x1ef2;" k="71" />
    <hkern u1="\" u2="&#x1ed7;" k="10" />
    <hkern u1="\" u2="&#x1ec5;" k="10" />
    <hkern u1="\" u2="&#x1e85;" k="17" />
    <hkern u1="\" u2="&#x1e84;" k="34" />
    <hkern u1="\" u2="&#x1e83;" k="17" />
    <hkern u1="\" u2="&#x1e82;" k="34" />
    <hkern u1="\" u2="&#x1e81;" k="17" />
    <hkern u1="\" u2="&#x1e80;" k="34" />
    <hkern u1="\" u2="&#x21b;" k="16" />
    <hkern u1="\" u2="&#x21a;" k="56" />
    <hkern u1="\" u2="&#x218;" k="10" />
    <hkern u1="\" u2="&#x1ff;" k="10" />
    <hkern u1="\" u2="&#x1fe;" k="20" />
    <hkern u1="\" u2="&#x178;" k="71" />
    <hkern u1="\" u2="&#x177;" k="22" />
    <hkern u1="\" u2="&#x176;" k="71" />
    <hkern u1="\" u2="&#x175;" k="17" />
    <hkern u1="\" u2="&#x174;" k="34" />
    <hkern u1="\" u2="&#x172;" k="22" />
    <hkern u1="\" u2="&#x170;" k="22" />
    <hkern u1="\" u2="&#x16e;" k="22" />
    <hkern u1="\" u2="&#x16c;" k="22" />
    <hkern u1="\" u2="&#x16a;" k="22" />
    <hkern u1="\" u2="&#x168;" k="22" />
    <hkern u1="\" u2="&#x167;" k="16" />
    <hkern u1="\" u2="&#x166;" k="56" />
    <hkern u1="\" u2="&#x165;" k="16" />
    <hkern u1="\" u2="&#x164;" k="56" />
    <hkern u1="\" u2="&#x160;" k="10" />
    <hkern u1="\" u2="&#x15e;" k="10" />
    <hkern u1="\" u2="&#x15c;" k="10" />
    <hkern u1="\" u2="&#x15a;" k="10" />
    <hkern u1="\" u2="&#x153;" k="10" />
    <hkern u1="\" u2="&#x152;" k="20" />
    <hkern u1="\" u2="&#x151;" k="10" />
    <hkern u1="\" u2="&#x150;" k="20" />
    <hkern u1="\" u2="&#x14f;" k="10" />
    <hkern u1="\" u2="&#x14e;" k="20" />
    <hkern u1="\" u2="&#x14d;" k="10" />
    <hkern u1="\" u2="&#x14c;" k="20" />
    <hkern u1="\" u2="&#x122;" k="20" />
    <hkern u1="\" u2="&#x120;" k="20" />
    <hkern u1="\" u2="&#x11e;" k="20" />
    <hkern u1="\" u2="&#x11c;" k="20" />
    <hkern u1="\" u2="&#x11b;" k="10" />
    <hkern u1="\" u2="&#x119;" k="10" />
    <hkern u1="\" u2="&#x117;" k="10" />
    <hkern u1="\" u2="&#x115;" k="10" />
    <hkern u1="\" u2="&#x113;" k="10" />
    <hkern u1="\" u2="&#x10d;" k="10" />
    <hkern u1="\" u2="&#x10c;" k="19" />
    <hkern u1="\" u2="&#x10b;" k="10" />
    <hkern u1="\" u2="&#x10a;" k="19" />
    <hkern u1="\" u2="&#x109;" k="10" />
    <hkern u1="\" u2="&#x108;" k="19" />
    <hkern u1="\" u2="&#x107;" k="10" />
    <hkern u1="\" u2="&#x106;" k="19" />
    <hkern u1="\" u2="&#xff;" k="22" />
    <hkern u1="\" u2="&#xfd;" k="22" />
    <hkern u1="\" u2="&#xf8;" k="10" />
    <hkern u1="\" u2="&#xf6;" k="10" />
    <hkern u1="\" u2="&#xf5;" k="10" />
    <hkern u1="\" u2="&#xf4;" k="10" />
    <hkern u1="\" u2="&#xf3;" k="10" />
    <hkern u1="\" u2="&#xf2;" k="10" />
    <hkern u1="\" u2="&#xeb;" k="10" />
    <hkern u1="\" u2="&#xea;" k="10" />
    <hkern u1="\" u2="&#xe9;" k="10" />
    <hkern u1="\" u2="&#xe8;" k="10" />
    <hkern u1="\" u2="&#xe7;" k="10" />
    <hkern u1="\" u2="&#xdd;" k="71" />
    <hkern u1="\" u2="&#xdc;" k="22" />
    <hkern u1="\" u2="&#xdb;" k="22" />
    <hkern u1="\" u2="&#xda;" k="22" />
    <hkern u1="\" u2="&#xd9;" k="22" />
    <hkern u1="\" u2="&#xd8;" k="20" />
    <hkern u1="\" u2="&#xd6;" k="20" />
    <hkern u1="\" u2="&#xd5;" k="20" />
    <hkern u1="\" u2="&#xd4;" k="20" />
    <hkern u1="\" u2="&#xd3;" k="20" />
    <hkern u1="\" u2="&#xd2;" k="20" />
    <hkern u1="\" u2="&#xc7;" k="19" />
    <hkern u1="\" u2="y" k="22" />
    <hkern u1="\" u2="w" k="17" />
    <hkern u1="\" u2="v" k="21" />
    <hkern u1="\" u2="t" k="16" />
    <hkern u1="\" u2="o" k="10" />
    <hkern u1="\" u2="f" k="12" />
    <hkern u1="\" u2="e" k="10" />
    <hkern u1="\" u2="c" k="10" />
    <hkern u1="\" u2="Y" k="71" />
    <hkern u1="\" u2="W" k="34" />
    <hkern u1="\" u2="V" k="45" />
    <hkern u1="\" u2="U" k="22" />
    <hkern u1="\" u2="T" k="56" />
    <hkern u1="\" u2="S" k="10" />
    <hkern u1="\" u2="Q" k="20" />
    <hkern u1="\" u2="O" k="20" />
    <hkern u1="\" u2="G" k="20" />
    <hkern u1="\" u2="C" k="19" />
    <hkern u1="\" u2="&#x27;" k="73" />
    <hkern u1="\" u2="&#x22;" k="73" />
    <hkern u1="a" u2="&#x2122;" k="15" />
    <hkern u1="a" u2="v" k="6" />
    <hkern u1="a" u2="\" k="43" />
    <hkern u1="a" u2="V" k="28" />
    <hkern u1="a" u2="&#x3f;" k="13" />
    <hkern u1="a" u2="&#x2a;" k="8" />
    <hkern u1="b" u2="&#x2122;" k="15" />
    <hkern u1="b" u2="&#xc6;" k="6" />
    <hkern u1="b" u2="&#x7d;" k="18" />
    <hkern u1="b" u2="x" k="8" />
    <hkern u1="b" u2="v" k="6" />
    <hkern u1="b" u2="]" k="26" />
    <hkern u1="b" u2="\" k="40" />
    <hkern u1="b" u2="X" k="22" />
    <hkern u1="b" u2="V" k="27" />
    <hkern u1="b" u2="&#x3f;" k="16" />
    <hkern u1="b" u2="&#x2a;" k="8" />
    <hkern u1="b" u2="&#x29;" k="19" />
    <hkern u1="c" u2="&#xf0;" k="7" />
    <hkern u1="c" u2="\" k="24" />
    <hkern u1="c" u2="V" k="12" />
    <hkern u1="d" u2="&#xef;" k="-8" />
    <hkern u1="d" u2="&#xec;" k="-18" />
    <hkern u1="e" u2="&#x2122;" k="12" />
    <hkern u1="e" u2="&#xc6;" k="5" />
    <hkern u1="e" u2="&#x7d;" k="10" />
    <hkern u1="e" u2="v" k="6" />
    <hkern u1="e" u2="\" k="38" />
    <hkern u1="e" u2="X" k="5" />
    <hkern u1="e" u2="V" k="25" />
    <hkern u1="e" u2="&#x3f;" k="11" />
    <hkern u1="e" u2="&#x29;" k="12" />
    <hkern u1="f" u2="&#x2039;" k="24" />
    <hkern u1="f" u2="&#x2026;" k="34" />
    <hkern u1="f" u2="&#x201e;" k="34" />
    <hkern u1="f" u2="&#x201a;" k="34" />
    <hkern u1="f" u2="&#x2014;" k="31" />
    <hkern u1="f" u2="&#x2013;" k="31" />
    <hkern u1="f" u2="&#x1ef8;" k="-13" />
    <hkern u1="f" u2="&#x1ef2;" k="-13" />
    <hkern u1="f" u2="&#x1ed7;" k="4" />
    <hkern u1="f" u2="&#x1ec5;" k="4" />
    <hkern u1="f" u2="&#x1eb0;" k="18" />
    <hkern u1="f" u2="&#x1ff;" k="4" />
    <hkern u1="f" u2="&#x1fc;" k="18" />
    <hkern u1="f" u2="&#x1fa;" k="18" />
    <hkern u1="f" u2="&#x178;" k="-13" />
    <hkern u1="f" u2="&#x176;" k="-13" />
    <hkern u1="f" u2="&#x153;" k="4" />
    <hkern u1="f" u2="&#x151;" k="4" />
    <hkern u1="f" u2="&#x14f;" k="4" />
    <hkern u1="f" u2="&#x14d;" k="4" />
    <hkern u1="f" u2="&#x135;" k="-37" />
    <hkern u1="f" u2="&#x134;" k="7" />
    <hkern u1="f" u2="&#x12d;" k="-39" />
    <hkern u1="f" u2="&#x12b;" k="-25" />
    <hkern u1="f" u2="&#x129;" k="-48" />
    <hkern u1="f" u2="&#x11b;" k="4" />
    <hkern u1="f" u2="&#x119;" k="4" />
    <hkern u1="f" u2="&#x117;" k="4" />
    <hkern u1="f" u2="&#x115;" k="4" />
    <hkern u1="f" u2="&#x113;" k="4" />
    <hkern u1="f" u2="&#x10d;" k="4" />
    <hkern u1="f" u2="&#x10b;" k="4" />
    <hkern u1="f" u2="&#x109;" k="4" />
    <hkern u1="f" u2="&#x107;" k="4" />
    <hkern u1="f" u2="&#x104;" k="18" />
    <hkern u1="f" u2="&#x102;" k="18" />
    <hkern u1="f" u2="&#x100;" k="18" />
    <hkern u1="f" u2="&#xf8;" k="4" />
    <hkern u1="f" u2="&#xf6;" k="4" />
    <hkern u1="f" u2="&#xf5;" k="4" />
    <hkern u1="f" u2="&#xf4;" k="4" />
    <hkern u1="f" u2="&#xf3;" k="4" />
    <hkern u1="f" u2="&#xf2;" k="4" />
    <hkern u1="f" u2="&#xf0;" k="19" />
    <hkern u1="f" u2="&#xef;" k="-59" />
    <hkern u1="f" u2="&#xee;" k="-44" />
    <hkern u1="f" u2="&#xec;" k="-68" />
    <hkern u1="f" u2="&#xeb;" k="4" />
    <hkern u1="f" u2="&#xea;" k="4" />
    <hkern u1="f" u2="&#xe9;" k="4" />
    <hkern u1="f" u2="&#xe8;" k="4" />
    <hkern u1="f" u2="&#xe7;" k="4" />
    <hkern u1="f" u2="&#xdd;" k="-13" />
    <hkern u1="f" u2="&#xc6;" k="24" />
    <hkern u1="f" u2="&#xc5;" k="18" />
    <hkern u1="f" u2="&#xc4;" k="18" />
    <hkern u1="f" u2="&#xc3;" k="18" />
    <hkern u1="f" u2="&#xc2;" k="18" />
    <hkern u1="f" u2="&#xc1;" k="18" />
    <hkern u1="f" u2="&#xc0;" k="18" />
    <hkern u1="f" u2="&#xab;" k="24" />
    <hkern u1="f" u2="o" k="4" />
    <hkern u1="f" u2="e" k="4" />
    <hkern u1="f" u2="c" k="4" />
    <hkern u1="f" u2="Y" k="-13" />
    <hkern u1="f" u2="J" k="7" />
    <hkern u1="f" u2="A" k="18" />
    <hkern u1="f" u2="&#x2f;" k="25" />
    <hkern u1="f" u2="&#x2e;" k="34" />
    <hkern u1="f" u2="&#x2d;" k="31" />
    <hkern u1="f" u2="&#x2c;" k="34" />
    <hkern u1="g" u2="&#x135;" k="-24" />
    <hkern u1="g" u2="&#xf0;" k="6" />
    <hkern u1="g" u2="j" k="-24" />
    <hkern u1="g" u2="\" k="15" />
    <hkern u1="g" u2="V" k="5" />
    <hkern u1="h" u2="&#x2122;" k="14" />
    <hkern u1="h" u2="v" k="4" />
    <hkern u1="h" u2="\" k="40" />
    <hkern u1="h" u2="V" k="27" />
    <hkern u1="h" u2="&#x3f;" k="14" />
    <hkern u1="h" u2="&#x2a;" k="8" />
    <hkern u1="h" u2="&#x29;" k="11" />
    <hkern u1="i" u2="&#xef;" k="-8" />
    <hkern u1="i" u2="&#xec;" k="-18" />
    <hkern u1="j" u2="&#xef;" k="-8" />
    <hkern u1="j" u2="&#xec;" k="-18" />
    <hkern u1="k" u2="&#xf0;" k="12" />
    <hkern u1="k" u2="\" k="17" />
    <hkern u1="k" u2="V" k="8" />
    <hkern u1="l" u2="&#xec;" k="-12" />
    <hkern u1="l" u2="&#xb7;" k="64" />
    <hkern u1="m" u2="&#x2122;" k="14" />
    <hkern u1="m" u2="v" k="4" />
    <hkern u1="m" u2="\" k="40" />
    <hkern u1="m" u2="V" k="27" />
    <hkern u1="m" u2="&#x3f;" k="14" />
    <hkern u1="m" u2="&#x2a;" k="8" />
    <hkern u1="m" u2="&#x29;" k="11" />
    <hkern u1="n" u2="&#x2122;" k="14" />
    <hkern u1="n" u2="v" k="4" />
    <hkern u1="n" u2="\" k="40" />
    <hkern u1="n" u2="V" k="27" />
    <hkern u1="n" u2="&#x3f;" k="14" />
    <hkern u1="n" u2="&#x2a;" k="8" />
    <hkern u1="n" u2="&#x29;" k="11" />
    <hkern u1="o" u2="&#x2122;" k="14" />
    <hkern u1="o" u2="&#xc6;" k="7" />
    <hkern u1="o" u2="&#x7d;" k="19" />
    <hkern u1="o" u2="x" k="10" />
    <hkern u1="o" u2="v" k="7" />
    <hkern u1="o" u2="]" k="27" />
    <hkern u1="o" u2="\" k="41" />
    <hkern u1="o" u2="X" k="26" />
    <hkern u1="o" u2="V" k="29" />
    <hkern u1="o" u2="&#x3f;" k="15" />
    <hkern u1="o" u2="&#x2a;" k="8" />
    <hkern u1="o" u2="&#x29;" k="20" />
    <hkern u1="p" u2="&#x2122;" k="15" />
    <hkern u1="p" u2="&#xc6;" k="6" />
    <hkern u1="p" u2="&#x7d;" k="18" />
    <hkern u1="p" u2="x" k="8" />
    <hkern u1="p" u2="v" k="6" />
    <hkern u1="p" u2="]" k="26" />
    <hkern u1="p" u2="\" k="40" />
    <hkern u1="p" u2="X" k="22" />
    <hkern u1="p" u2="V" k="27" />
    <hkern u1="p" u2="&#x3f;" k="16" />
    <hkern u1="p" u2="&#x2a;" k="8" />
    <hkern u1="p" u2="&#x29;" k="19" />
    <hkern u1="q" u2="&#x2122;" k="9" />
    <hkern u1="q" u2="\" k="28" />
    <hkern u1="q" u2="X" k="5" />
    <hkern u1="q" u2="V" k="21" />
    <hkern u1="q" u2="&#x29;" k="11" />
    <hkern u1="r" u2="&#xf0;" k="19" />
    <hkern u1="r" u2="&#xc6;" k="31" />
    <hkern u1="r" u2="&#x7d;" k="12" />
    <hkern u1="r" u2="]" k="19" />
    <hkern u1="r" u2="\" k="11" />
    <hkern u1="r" u2="X" k="29" />
    <hkern u1="r" u2="&#x2f;" k="33" />
    <hkern u1="r" u2="&#x29;" k="10" />
    <hkern u1="s" u2="&#x2122;" k="11" />
    <hkern u1="s" u2="&#xc6;" k="5" />
    <hkern u1="s" u2="&#x7d;" k="12" />
    <hkern u1="s" u2="v" k="5" />
    <hkern u1="s" u2="]" k="18" />
    <hkern u1="s" u2="\" k="29" />
    <hkern u1="s" u2="X" k="8" />
    <hkern u1="s" u2="V" k="18" />
    <hkern u1="s" u2="&#x29;" k="13" />
    <hkern u1="t" u2="\" k="17" />
    <hkern u1="t" u2="V" k="6" />
    <hkern u1="u" u2="&#x2122;" k="9" />
    <hkern u1="u" u2="\" k="28" />
    <hkern u1="u" u2="X" k="5" />
    <hkern u1="u" u2="V" k="21" />
    <hkern u1="u" u2="&#x29;" k="11" />
    <hkern u1="v" u2="&#x2039;" k="10" />
    <hkern u1="v" u2="&#x2026;" k="23" />
    <hkern u1="v" u2="&#x201e;" k="23" />
    <hkern u1="v" u2="&#x201a;" k="23" />
    <hkern u1="v" u2="&#x2014;" k="9" />
    <hkern u1="v" u2="&#x2013;" k="9" />
    <hkern u1="v" u2="&#x1ef8;" k="33" />
    <hkern u1="v" u2="&#x1ef2;" k="33" />
    <hkern u1="v" u2="&#x1ed7;" k="7" />
    <hkern u1="v" u2="&#x1ec5;" k="7" />
    <hkern u1="v" u2="&#x1eb0;" k="14" />
    <hkern u1="v" u2="&#x1eab;" k="6" />
    <hkern u1="v" u2="&#x21a;" k="50" />
    <hkern u1="v" u2="&#x219;" k="5" />
    <hkern u1="v" u2="&#x1ff;" k="7" />
    <hkern u1="v" u2="&#x1fd;" k="6" />
    <hkern u1="v" u2="&#x1fc;" k="14" />
    <hkern u1="v" u2="&#x1fb;" k="6" />
    <hkern u1="v" u2="&#x1fa;" k="14" />
    <hkern u1="v" u2="&#x17d;" k="7" />
    <hkern u1="v" u2="&#x17b;" k="7" />
    <hkern u1="v" u2="&#x179;" k="7" />
    <hkern u1="v" u2="&#x178;" k="33" />
    <hkern u1="v" u2="&#x176;" k="33" />
    <hkern u1="v" u2="&#x166;" k="50" />
    <hkern u1="v" u2="&#x164;" k="50" />
    <hkern u1="v" u2="&#x161;" k="5" />
    <hkern u1="v" u2="&#x15f;" k="5" />
    <hkern u1="v" u2="&#x15d;" k="5" />
    <hkern u1="v" u2="&#x15b;" k="5" />
    <hkern u1="v" u2="&#x153;" k="7" />
    <hkern u1="v" u2="&#x151;" k="7" />
    <hkern u1="v" u2="&#x14f;" k="7" />
    <hkern u1="v" u2="&#x14d;" k="7" />
    <hkern u1="v" u2="&#x134;" k="12" />
    <hkern u1="v" u2="&#x123;" k="7" />
    <hkern u1="v" u2="&#x121;" k="7" />
    <hkern u1="v" u2="&#x11f;" k="7" />
    <hkern u1="v" u2="&#x11d;" k="7" />
    <hkern u1="v" u2="&#x11b;" k="7" />
    <hkern u1="v" u2="&#x119;" k="7" />
    <hkern u1="v" u2="&#x117;" k="7" />
    <hkern u1="v" u2="&#x115;" k="7" />
    <hkern u1="v" u2="&#x113;" k="7" />
    <hkern u1="v" u2="&#x111;" k="6" />
    <hkern u1="v" u2="&#x10f;" k="6" />
    <hkern u1="v" u2="&#x10d;" k="7" />
    <hkern u1="v" u2="&#x10b;" k="7" />
    <hkern u1="v" u2="&#x109;" k="7" />
    <hkern u1="v" u2="&#x107;" k="7" />
    <hkern u1="v" u2="&#x105;" k="6" />
    <hkern u1="v" u2="&#x104;" k="14" />
    <hkern u1="v" u2="&#x103;" k="6" />
    <hkern u1="v" u2="&#x102;" k="14" />
    <hkern u1="v" u2="&#x101;" k="6" />
    <hkern u1="v" u2="&#x100;" k="14" />
    <hkern u1="v" u2="&#xf8;" k="7" />
    <hkern u1="v" u2="&#xf6;" k="7" />
    <hkern u1="v" u2="&#xf5;" k="7" />
    <hkern u1="v" u2="&#xf4;" k="7" />
    <hkern u1="v" u2="&#xf3;" k="7" />
    <hkern u1="v" u2="&#xf2;" k="7" />
    <hkern u1="v" u2="&#xf0;" k="11" />
    <hkern u1="v" u2="&#xeb;" k="7" />
    <hkern u1="v" u2="&#xea;" k="7" />
    <hkern u1="v" u2="&#xe9;" k="7" />
    <hkern u1="v" u2="&#xe8;" k="7" />
    <hkern u1="v" u2="&#xe7;" k="7" />
    <hkern u1="v" u2="&#xe6;" k="6" />
    <hkern u1="v" u2="&#xe5;" k="6" />
    <hkern u1="v" u2="&#xe4;" k="6" />
    <hkern u1="v" u2="&#xe3;" k="6" />
    <hkern u1="v" u2="&#xe2;" k="6" />
    <hkern u1="v" u2="&#xe1;" k="6" />
    <hkern u1="v" u2="&#xe0;" k="6" />
    <hkern u1="v" u2="&#xdd;" k="33" />
    <hkern u1="v" u2="&#xc6;" k="17" />
    <hkern u1="v" u2="&#xc5;" k="14" />
    <hkern u1="v" u2="&#xc4;" k="14" />
    <hkern u1="v" u2="&#xc3;" k="14" />
    <hkern u1="v" u2="&#xc2;" k="14" />
    <hkern u1="v" u2="&#xc1;" k="14" />
    <hkern u1="v" u2="&#xc0;" k="14" />
    <hkern u1="v" u2="&#xab;" k="10" />
    <hkern u1="v" u2="&#x7d;" k="12" />
    <hkern u1="v" u2="s" k="5" />
    <hkern u1="v" u2="q" k="6" />
    <hkern u1="v" u2="o" k="7" />
    <hkern u1="v" u2="g" k="7" />
    <hkern u1="v" u2="e" k="7" />
    <hkern u1="v" u2="d" k="6" />
    <hkern u1="v" u2="c" k="7" />
    <hkern u1="v" u2="a" k="6" />
    <hkern u1="v" u2="]" k="20" />
    <hkern u1="v" u2="\" k="18" />
    <hkern u1="v" u2="Z" k="7" />
    <hkern u1="v" u2="Y" k="33" />
    <hkern u1="v" u2="X" k="22" />
    <hkern u1="v" u2="V" k="6" />
    <hkern u1="v" u2="T" k="50" />
    <hkern u1="v" u2="J" k="12" />
    <hkern u1="v" u2="A" k="14" />
    <hkern u1="v" u2="&#x2f;" k="18" />
    <hkern u1="v" u2="&#x2e;" k="23" />
    <hkern u1="v" u2="&#x2d;" k="9" />
    <hkern u1="v" u2="&#x2c;" k="23" />
    <hkern u1="v" u2="&#x29;" k="11" />
    <hkern u1="w" u2="&#xf0;" k="7" />
    <hkern u1="w" u2="&#xc6;" k="14" />
    <hkern u1="w" u2="&#x7d;" k="15" />
    <hkern u1="w" u2="]" k="21" />
    <hkern u1="w" u2="\" k="18" />
    <hkern u1="w" u2="X" k="23" />
    <hkern u1="w" u2="V" k="9" />
    <hkern u1="w" u2="&#x2f;" k="14" />
    <hkern u1="w" u2="&#x29;" k="14" />
    <hkern u1="x" u2="&#x2039;" k="21" />
    <hkern u1="x" u2="&#x2014;" k="24" />
    <hkern u1="x" u2="&#x2013;" k="24" />
    <hkern u1="x" u2="&#x1ef8;" k="32" />
    <hkern u1="x" u2="&#x1ef2;" k="32" />
    <hkern u1="x" u2="&#x1ed7;" k="10" />
    <hkern u1="x" u2="&#x1ec5;" k="10" />
    <hkern u1="x" u2="&#x21a;" k="48" />
    <hkern u1="x" u2="&#x1ff;" k="10" />
    <hkern u1="x" u2="&#x178;" k="32" />
    <hkern u1="x" u2="&#x176;" k="32" />
    <hkern u1="x" u2="&#x166;" k="48" />
    <hkern u1="x" u2="&#x164;" k="48" />
    <hkern u1="x" u2="&#x153;" k="10" />
    <hkern u1="x" u2="&#x151;" k="10" />
    <hkern u1="x" u2="&#x14f;" k="10" />
    <hkern u1="x" u2="&#x14d;" k="10" />
    <hkern u1="x" u2="&#x123;" k="8" />
    <hkern u1="x" u2="&#x121;" k="8" />
    <hkern u1="x" u2="&#x11f;" k="8" />
    <hkern u1="x" u2="&#x11d;" k="8" />
    <hkern u1="x" u2="&#x11b;" k="10" />
    <hkern u1="x" u2="&#x119;" k="10" />
    <hkern u1="x" u2="&#x117;" k="10" />
    <hkern u1="x" u2="&#x115;" k="10" />
    <hkern u1="x" u2="&#x113;" k="10" />
    <hkern u1="x" u2="&#x111;" k="9" />
    <hkern u1="x" u2="&#x10f;" k="9" />
    <hkern u1="x" u2="&#x10d;" k="10" />
    <hkern u1="x" u2="&#x10b;" k="10" />
    <hkern u1="x" u2="&#x109;" k="10" />
    <hkern u1="x" u2="&#x107;" k="10" />
    <hkern u1="x" u2="&#xf8;" k="10" />
    <hkern u1="x" u2="&#xf6;" k="10" />
    <hkern u1="x" u2="&#xf5;" k="10" />
    <hkern u1="x" u2="&#xf4;" k="10" />
    <hkern u1="x" u2="&#xf3;" k="10" />
    <hkern u1="x" u2="&#xf2;" k="10" />
    <hkern u1="x" u2="&#xf0;" k="16" />
    <hkern u1="x" u2="&#xeb;" k="10" />
    <hkern u1="x" u2="&#xea;" k="10" />
    <hkern u1="x" u2="&#xe9;" k="10" />
    <hkern u1="x" u2="&#xe8;" k="10" />
    <hkern u1="x" u2="&#xe7;" k="10" />
    <hkern u1="x" u2="&#xdd;" k="32" />
    <hkern u1="x" u2="&#xab;" k="21" />
    <hkern u1="x" u2="q" k="9" />
    <hkern u1="x" u2="o" k="10" />
    <hkern u1="x" u2="g" k="8" />
    <hkern u1="x" u2="e" k="10" />
    <hkern u1="x" u2="d" k="9" />
    <hkern u1="x" u2="c" k="10" />
    <hkern u1="x" u2="\" k="15" />
    <hkern u1="x" u2="Y" k="32" />
    <hkern u1="x" u2="V" k="6" />
    <hkern u1="x" u2="T" k="48" />
    <hkern u1="x" u2="&#x2d;" k="24" />
    <hkern u1="y" u2="&#xf0;" k="13" />
    <hkern u1="y" u2="&#xc6;" k="18" />
    <hkern u1="y" u2="&#x7d;" k="11" />
    <hkern u1="y" u2="]" k="19" />
    <hkern u1="y" u2="\" k="18" />
    <hkern u1="y" u2="X" k="22" />
    <hkern u1="y" u2="V" k="6" />
    <hkern u1="y" u2="&#x2f;" k="19" />
    <hkern u1="z" u2="&#xf0;" k="4" />
    <hkern u1="z" u2="\" k="25" />
    <hkern u1="z" u2="V" k="13" />
    <hkern u1="&#x7b;" u2="&#x1ef9;" k="12" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="12" />
    <hkern u1="&#x7b;" u2="&#x1ed7;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1ec5;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1eab;" k="12" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="15" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="15" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="15" />
    <hkern u1="&#x7b;" u2="&#x1ff;" k="19" />
    <hkern u1="&#x7b;" u2="&#x1fe;" k="14" />
    <hkern u1="&#x7b;" u2="&#x1fd;" k="12" />
    <hkern u1="&#x7b;" u2="&#x1fb;" k="12" />
    <hkern u1="&#x7b;" u2="&#x177;" k="12" />
    <hkern u1="&#x7b;" u2="&#x175;" k="15" />
    <hkern u1="&#x7b;" u2="&#x173;" k="15" />
    <hkern u1="&#x7b;" u2="&#x171;" k="15" />
    <hkern u1="&#x7b;" u2="&#x16f;" k="15" />
    <hkern u1="&#x7b;" u2="&#x16d;" k="15" />
    <hkern u1="&#x7b;" u2="&#x16b;" k="15" />
    <hkern u1="&#x7b;" u2="&#x169;" k="15" />
    <hkern u1="&#x7b;" u2="&#x153;" k="19" />
    <hkern u1="&#x7b;" u2="&#x152;" k="14" />
    <hkern u1="&#x7b;" u2="&#x151;" k="19" />
    <hkern u1="&#x7b;" u2="&#x150;" k="14" />
    <hkern u1="&#x7b;" u2="&#x14f;" k="19" />
    <hkern u1="&#x7b;" u2="&#x14e;" k="14" />
    <hkern u1="&#x7b;" u2="&#x14d;" k="19" />
    <hkern u1="&#x7b;" u2="&#x14c;" k="14" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-21" />
    <hkern u1="&#x7b;" u2="&#x12d;" k="-24" />
    <hkern u1="&#x7b;" u2="&#x129;" k="-27" />
    <hkern u1="&#x7b;" u2="&#x122;" k="14" />
    <hkern u1="&#x7b;" u2="&#x120;" k="14" />
    <hkern u1="&#x7b;" u2="&#x11e;" k="14" />
    <hkern u1="&#x7b;" u2="&#x11c;" k="14" />
    <hkern u1="&#x7b;" u2="&#x11b;" k="19" />
    <hkern u1="&#x7b;" u2="&#x119;" k="19" />
    <hkern u1="&#x7b;" u2="&#x117;" k="19" />
    <hkern u1="&#x7b;" u2="&#x115;" k="19" />
    <hkern u1="&#x7b;" u2="&#x113;" k="19" />
    <hkern u1="&#x7b;" u2="&#x111;" k="18" />
    <hkern u1="&#x7b;" u2="&#x10f;" k="18" />
    <hkern u1="&#x7b;" u2="&#x10d;" k="19" />
    <hkern u1="&#x7b;" u2="&#x10c;" k="12" />
    <hkern u1="&#x7b;" u2="&#x10b;" k="19" />
    <hkern u1="&#x7b;" u2="&#x10a;" k="12" />
    <hkern u1="&#x7b;" u2="&#x109;" k="19" />
    <hkern u1="&#x7b;" u2="&#x108;" k="12" />
    <hkern u1="&#x7b;" u2="&#x107;" k="19" />
    <hkern u1="&#x7b;" u2="&#x106;" k="12" />
    <hkern u1="&#x7b;" u2="&#x105;" k="12" />
    <hkern u1="&#x7b;" u2="&#x103;" k="12" />
    <hkern u1="&#x7b;" u2="&#x101;" k="12" />
    <hkern u1="&#x7b;" u2="&#xff;" k="12" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="12" />
    <hkern u1="&#x7b;" u2="&#xfc;" k="15" />
    <hkern u1="&#x7b;" u2="&#xfb;" k="15" />
    <hkern u1="&#x7b;" u2="&#xfa;" k="15" />
    <hkern u1="&#x7b;" u2="&#xf9;" k="15" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="19" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="19" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="19" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="19" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="19" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="19" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-37" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-45" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="19" />
    <hkern u1="&#x7b;" u2="&#xea;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="19" />
    <hkern u1="&#x7b;" u2="&#xe6;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe5;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe4;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe3;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe2;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe1;" k="12" />
    <hkern u1="&#x7b;" u2="&#xe0;" k="12" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="14" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="14" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="14" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="14" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="14" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="14" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="12" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="12" />
    <hkern u1="&#x7b;" u2="y" k="12" />
    <hkern u1="&#x7b;" u2="w" k="15" />
    <hkern u1="&#x7b;" u2="v" k="12" />
    <hkern u1="&#x7b;" u2="u" k="15" />
    <hkern u1="&#x7b;" u2="q" k="18" />
    <hkern u1="&#x7b;" u2="o" k="19" />
    <hkern u1="&#x7b;" u2="j" k="-21" />
    <hkern u1="&#x7b;" u2="e" k="19" />
    <hkern u1="&#x7b;" u2="d" k="18" />
    <hkern u1="&#x7b;" u2="c" k="19" />
    <hkern u1="&#x7b;" u2="a" k="12" />
    <hkern u1="&#x7b;" u2="Q" k="14" />
    <hkern u1="&#x7b;" u2="O" k="14" />
    <hkern u1="&#x7b;" u2="G" k="14" />
    <hkern u1="&#x7b;" u2="C" k="12" />
    <hkern u1="&#x7c;" u2="&#xec;" k="-15" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="12" />
    <hkern u1="&#x7d;" u2="]" k="18" />
    <hkern u1="&#x7d;" u2="&#x29;" k="13" />
    <hkern u1="&#xa1;" u2="&#x1ef8;" k="27" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="27" />
    <hkern u1="&#xa1;" u2="&#x21a;" k="30" />
    <hkern u1="&#xa1;" u2="&#x178;" k="27" />
    <hkern u1="&#xa1;" u2="&#x176;" k="27" />
    <hkern u1="&#xa1;" u2="&#x166;" k="30" />
    <hkern u1="&#xa1;" u2="&#x164;" k="30" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="27" />
    <hkern u1="&#xa1;" u2="Y" k="27" />
    <hkern u1="&#xa1;" u2="T" k="30" />
    <hkern u1="&#xab;" u2="V" k="11" />
    <hkern u1="&#xae;" u2="&#x1ef8;" k="19" />
    <hkern u1="&#xae;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#xae;" u2="&#x1eb0;" k="20" />
    <hkern u1="&#xae;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xae;" u2="&#x1fa;" k="20" />
    <hkern u1="&#xae;" u2="&#x178;" k="19" />
    <hkern u1="&#xae;" u2="&#x176;" k="19" />
    <hkern u1="&#xae;" u2="&#x134;" k="13" />
    <hkern u1="&#xae;" u2="&#x104;" k="20" />
    <hkern u1="&#xae;" u2="&#x102;" k="20" />
    <hkern u1="&#xae;" u2="&#x100;" k="20" />
    <hkern u1="&#xae;" u2="&#xdd;" k="19" />
    <hkern u1="&#xae;" u2="&#xc6;" k="26" />
    <hkern u1="&#xae;" u2="&#xc5;" k="20" />
    <hkern u1="&#xae;" u2="&#xc4;" k="20" />
    <hkern u1="&#xae;" u2="&#xc3;" k="20" />
    <hkern u1="&#xae;" u2="&#xc2;" k="20" />
    <hkern u1="&#xae;" u2="&#xc1;" k="20" />
    <hkern u1="&#xae;" u2="&#xc0;" k="20" />
    <hkern u1="&#xae;" u2="Y" k="19" />
    <hkern u1="&#xae;" u2="J" k="13" />
    <hkern u1="&#xae;" u2="A" k="20" />
    <hkern u1="&#xb7;" u2="&#x142;" k="64" />
    <hkern u1="&#xb7;" u2="&#x13e;" k="64" />
    <hkern u1="&#xb7;" u2="&#x13c;" k="64" />
    <hkern u1="&#xb7;" u2="&#x13a;" k="64" />
    <hkern u1="&#xb7;" u2="l" k="64" />
    <hkern u1="&#xbb;" u2="&#x141;" k="-7" />
    <hkern u1="&#xbb;" u2="x" k="21" />
    <hkern u1="&#xbb;" u2="f" k="10" />
    <hkern u1="&#xbb;" u2="X" k="20" />
    <hkern u1="&#xbb;" u2="V" k="23" />
    <hkern u1="&#xbf;" u2="&#x1ef9;" k="25" />
    <hkern u1="&#xbf;" u2="&#x1ef8;" k="56" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="25" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="56" />
    <hkern u1="&#xbf;" u2="&#x1ed7;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1ec5;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1eb0;" k="16" />
    <hkern u1="&#xbf;" u2="&#x1eab;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="26" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="26" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="59" />
    <hkern u1="&#xbf;" u2="&#x219;" k="20" />
    <hkern u1="&#xbf;" u2="&#x218;" k="12" />
    <hkern u1="&#xbf;" u2="&#x1ff;" k="22" />
    <hkern u1="&#xbf;" u2="&#x1fe;" k="17" />
    <hkern u1="&#xbf;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1fc;" k="16" />
    <hkern u1="&#xbf;" u2="&#x1fb;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1fa;" k="16" />
    <hkern u1="&#xbf;" u2="&#x17e;" k="18" />
    <hkern u1="&#xbf;" u2="&#x17d;" k="16" />
    <hkern u1="&#xbf;" u2="&#x17c;" k="18" />
    <hkern u1="&#xbf;" u2="&#x17b;" k="16" />
    <hkern u1="&#xbf;" u2="&#x17a;" k="18" />
    <hkern u1="&#xbf;" u2="&#x179;" k="16" />
    <hkern u1="&#xbf;" u2="&#x178;" k="56" />
    <hkern u1="&#xbf;" u2="&#x177;" k="25" />
    <hkern u1="&#xbf;" u2="&#x176;" k="56" />
    <hkern u1="&#xbf;" u2="&#x175;" k="22" />
    <hkern u1="&#xbf;" u2="&#x174;" k="26" />
    <hkern u1="&#xbf;" u2="&#x173;" k="20" />
    <hkern u1="&#xbf;" u2="&#x172;" k="18" />
    <hkern u1="&#xbf;" u2="&#x171;" k="20" />
    <hkern u1="&#xbf;" u2="&#x170;" k="18" />
    <hkern u1="&#xbf;" u2="&#x16f;" k="20" />
    <hkern u1="&#xbf;" u2="&#x16e;" k="18" />
    <hkern u1="&#xbf;" u2="&#x16d;" k="20" />
    <hkern u1="&#xbf;" u2="&#x16c;" k="18" />
    <hkern u1="&#xbf;" u2="&#x16b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x16a;" k="18" />
    <hkern u1="&#xbf;" u2="&#x169;" k="20" />
    <hkern u1="&#xbf;" u2="&#x168;" k="18" />
    <hkern u1="&#xbf;" u2="&#x167;" k="20" />
    <hkern u1="&#xbf;" u2="&#x166;" k="59" />
    <hkern u1="&#xbf;" u2="&#x165;" k="20" />
    <hkern u1="&#xbf;" u2="&#x164;" k="59" />
    <hkern u1="&#xbf;" u2="&#x161;" k="20" />
    <hkern u1="&#xbf;" u2="&#x160;" k="12" />
    <hkern u1="&#xbf;" u2="&#x15f;" k="20" />
    <hkern u1="&#xbf;" u2="&#x15e;" k="12" />
    <hkern u1="&#xbf;" u2="&#x15d;" k="20" />
    <hkern u1="&#xbf;" u2="&#x15c;" k="12" />
    <hkern u1="&#xbf;" u2="&#x15b;" k="20" />
    <hkern u1="&#xbf;" u2="&#x15a;" k="12" />
    <hkern u1="&#xbf;" u2="&#x159;" k="18" />
    <hkern u1="&#xbf;" u2="&#x158;" k="14" />
    <hkern u1="&#xbf;" u2="&#x157;" k="18" />
    <hkern u1="&#xbf;" u2="&#x156;" k="14" />
    <hkern u1="&#xbf;" u2="&#x155;" k="18" />
    <hkern u1="&#xbf;" u2="&#x154;" k="14" />
    <hkern u1="&#xbf;" u2="&#x153;" k="22" />
    <hkern u1="&#xbf;" u2="&#x152;" k="17" />
    <hkern u1="&#xbf;" u2="&#x151;" k="22" />
    <hkern u1="&#xbf;" u2="&#x150;" k="17" />
    <hkern u1="&#xbf;" u2="&#x14f;" k="22" />
    <hkern u1="&#xbf;" u2="&#x14e;" k="17" />
    <hkern u1="&#xbf;" u2="&#x14d;" k="22" />
    <hkern u1="&#xbf;" u2="&#x14c;" k="17" />
    <hkern u1="&#xbf;" u2="&#x14b;" k="18" />
    <hkern u1="&#xbf;" u2="&#x14a;" k="14" />
    <hkern u1="&#xbf;" u2="&#x148;" k="18" />
    <hkern u1="&#xbf;" u2="&#x147;" k="14" />
    <hkern u1="&#xbf;" u2="&#x146;" k="18" />
    <hkern u1="&#xbf;" u2="&#x145;" k="14" />
    <hkern u1="&#xbf;" u2="&#x144;" k="18" />
    <hkern u1="&#xbf;" u2="&#x143;" k="14" />
    <hkern u1="&#xbf;" u2="&#x142;" k="18" />
    <hkern u1="&#xbf;" u2="&#x141;" k="14" />
    <hkern u1="&#xbf;" u2="&#x13e;" k="18" />
    <hkern u1="&#xbf;" u2="&#x13d;" k="14" />
    <hkern u1="&#xbf;" u2="&#x13c;" k="18" />
    <hkern u1="&#xbf;" u2="&#x13b;" k="14" />
    <hkern u1="&#xbf;" u2="&#x13a;" k="18" />
    <hkern u1="&#xbf;" u2="&#x139;" k="14" />
    <hkern u1="&#xbf;" u2="&#x137;" k="18" />
    <hkern u1="&#xbf;" u2="&#x136;" k="14" />
    <hkern u1="&#xbf;" u2="&#x135;" k="18" />
    <hkern u1="&#xbf;" u2="&#x131;" k="18" />
    <hkern u1="&#xbf;" u2="&#x130;" k="14" />
    <hkern u1="&#xbf;" u2="&#x12f;" k="11" />
    <hkern u1="&#xbf;" u2="&#x12e;" k="14" />
    <hkern u1="&#xbf;" u2="&#x12d;" k="18" />
    <hkern u1="&#xbf;" u2="&#x12c;" k="14" />
    <hkern u1="&#xbf;" u2="&#x12b;" k="18" />
    <hkern u1="&#xbf;" u2="&#x12a;" k="14" />
    <hkern u1="&#xbf;" u2="&#x129;" k="18" />
    <hkern u1="&#xbf;" u2="&#x128;" k="14" />
    <hkern u1="&#xbf;" u2="&#x127;" k="18" />
    <hkern u1="&#xbf;" u2="&#x126;" k="14" />
    <hkern u1="&#xbf;" u2="&#x125;" k="18" />
    <hkern u1="&#xbf;" u2="&#x124;" k="14" />
    <hkern u1="&#xbf;" u2="&#x122;" k="17" />
    <hkern u1="&#xbf;" u2="&#x120;" k="17" />
    <hkern u1="&#xbf;" u2="&#x11e;" k="17" />
    <hkern u1="&#xbf;" u2="&#x11c;" k="17" />
    <hkern u1="&#xbf;" u2="&#x11b;" k="22" />
    <hkern u1="&#xbf;" u2="&#x11a;" k="14" />
    <hkern u1="&#xbf;" u2="&#x119;" k="22" />
    <hkern u1="&#xbf;" u2="&#x118;" k="14" />
    <hkern u1="&#xbf;" u2="&#x117;" k="22" />
    <hkern u1="&#xbf;" u2="&#x116;" k="14" />
    <hkern u1="&#xbf;" u2="&#x115;" k="22" />
    <hkern u1="&#xbf;" u2="&#x114;" k="14" />
    <hkern u1="&#xbf;" u2="&#x113;" k="22" />
    <hkern u1="&#xbf;" u2="&#x112;" k="14" />
    <hkern u1="&#xbf;" u2="&#x111;" k="22" />
    <hkern u1="&#xbf;" u2="&#x110;" k="14" />
    <hkern u1="&#xbf;" u2="&#x10f;" k="22" />
    <hkern u1="&#xbf;" u2="&#x10e;" k="14" />
    <hkern u1="&#xbf;" u2="&#x10d;" k="22" />
    <hkern u1="&#xbf;" u2="&#x10c;" k="16" />
    <hkern u1="&#xbf;" u2="&#x10b;" k="22" />
    <hkern u1="&#xbf;" u2="&#x10a;" k="16" />
    <hkern u1="&#xbf;" u2="&#x109;" k="22" />
    <hkern u1="&#xbf;" u2="&#x108;" k="16" />
    <hkern u1="&#xbf;" u2="&#x107;" k="22" />
    <hkern u1="&#xbf;" u2="&#x106;" k="16" />
    <hkern u1="&#xbf;" u2="&#x105;" k="20" />
    <hkern u1="&#xbf;" u2="&#x104;" k="16" />
    <hkern u1="&#xbf;" u2="&#x103;" k="20" />
    <hkern u1="&#xbf;" u2="&#x102;" k="16" />
    <hkern u1="&#xbf;" u2="&#x101;" k="20" />
    <hkern u1="&#xbf;" u2="&#x100;" k="16" />
    <hkern u1="&#xbf;" u2="&#xff;" k="25" />
    <hkern u1="&#xbf;" u2="&#xfe;" k="18" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="25" />
    <hkern u1="&#xbf;" u2="&#xfc;" k="20" />
    <hkern u1="&#xbf;" u2="&#xfb;" k="20" />
    <hkern u1="&#xbf;" u2="&#xfa;" k="20" />
    <hkern u1="&#xbf;" u2="&#xf9;" k="20" />
    <hkern u1="&#xbf;" u2="&#xf8;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="22" />
    <hkern u1="&#xbf;" u2="&#xf1;" k="18" />
    <hkern u1="&#xbf;" u2="&#xf0;" k="24" />
    <hkern u1="&#xbf;" u2="&#xef;" k="18" />
    <hkern u1="&#xbf;" u2="&#xee;" k="18" />
    <hkern u1="&#xbf;" u2="&#xed;" k="18" />
    <hkern u1="&#xbf;" u2="&#xec;" k="18" />
    <hkern u1="&#xbf;" u2="&#xeb;" k="22" />
    <hkern u1="&#xbf;" u2="&#xea;" k="22" />
    <hkern u1="&#xbf;" u2="&#xe9;" k="22" />
    <hkern u1="&#xbf;" u2="&#xe8;" k="22" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="22" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe5;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe4;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe3;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe2;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe1;" k="20" />
    <hkern u1="&#xbf;" u2="&#xe0;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="18" />
    <hkern u1="&#xbf;" u2="&#xde;" k="14" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="56" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="18" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="18" />
    <hkern u1="&#xbf;" u2="&#xda;" k="18" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="18" />
    <hkern u1="&#xbf;" u2="&#xd8;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="17" />
    <hkern u1="&#xbf;" u2="&#xd1;" k="14" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="14" />
    <hkern u1="&#xbf;" u2="&#xcf;" k="14" />
    <hkern u1="&#xbf;" u2="&#xce;" k="14" />
    <hkern u1="&#xbf;" u2="&#xcd;" k="14" />
    <hkern u1="&#xbf;" u2="&#xcc;" k="14" />
    <hkern u1="&#xbf;" u2="&#xcb;" k="14" />
    <hkern u1="&#xbf;" u2="&#xca;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc9;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc8;" k="14" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc6;" k="17" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="16" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="16" />
    <hkern u1="&#xbf;" u2="z" k="18" />
    <hkern u1="&#xbf;" u2="y" k="25" />
    <hkern u1="&#xbf;" u2="x" k="17" />
    <hkern u1="&#xbf;" u2="w" k="22" />
    <hkern u1="&#xbf;" u2="v" k="24" />
    <hkern u1="&#xbf;" u2="u" k="20" />
    <hkern u1="&#xbf;" u2="t" k="20" />
    <hkern u1="&#xbf;" u2="s" k="20" />
    <hkern u1="&#xbf;" u2="r" k="18" />
    <hkern u1="&#xbf;" u2="q" k="22" />
    <hkern u1="&#xbf;" u2="p" k="18" />
    <hkern u1="&#xbf;" u2="o" k="22" />
    <hkern u1="&#xbf;" u2="n" k="18" />
    <hkern u1="&#xbf;" u2="m" k="18" />
    <hkern u1="&#xbf;" u2="l" k="18" />
    <hkern u1="&#xbf;" u2="k" k="18" />
    <hkern u1="&#xbf;" u2="j" k="18" />
    <hkern u1="&#xbf;" u2="i" k="18" />
    <hkern u1="&#xbf;" u2="h" k="18" />
    <hkern u1="&#xbf;" u2="f" k="20" />
    <hkern u1="&#xbf;" u2="e" k="22" />
    <hkern u1="&#xbf;" u2="d" k="22" />
    <hkern u1="&#xbf;" u2="c" k="22" />
    <hkern u1="&#xbf;" u2="b" k="18" />
    <hkern u1="&#xbf;" u2="a" k="20" />
    <hkern u1="&#xbf;" u2="Z" k="16" />
    <hkern u1="&#xbf;" u2="Y" k="56" />
    <hkern u1="&#xbf;" u2="X" k="18" />
    <hkern u1="&#xbf;" u2="W" k="26" />
    <hkern u1="&#xbf;" u2="V" k="33" />
    <hkern u1="&#xbf;" u2="U" k="18" />
    <hkern u1="&#xbf;" u2="T" k="59" />
    <hkern u1="&#xbf;" u2="S" k="12" />
    <hkern u1="&#xbf;" u2="R" k="14" />
    <hkern u1="&#xbf;" u2="Q" k="17" />
    <hkern u1="&#xbf;" u2="P" k="14" />
    <hkern u1="&#xbf;" u2="O" k="17" />
    <hkern u1="&#xbf;" u2="N" k="14" />
    <hkern u1="&#xbf;" u2="M" k="14" />
    <hkern u1="&#xbf;" u2="L" k="14" />
    <hkern u1="&#xbf;" u2="K" k="14" />
    <hkern u1="&#xbf;" u2="I" k="14" />
    <hkern u1="&#xbf;" u2="H" k="14" />
    <hkern u1="&#xbf;" u2="G" k="17" />
    <hkern u1="&#xbf;" u2="F" k="14" />
    <hkern u1="&#xbf;" u2="E" k="14" />
    <hkern u1="&#xbf;" u2="D" k="14" />
    <hkern u1="&#xbf;" u2="C" k="16" />
    <hkern u1="&#xbf;" u2="B" k="14" />
    <hkern u1="&#xbf;" u2="A" k="16" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc0;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc0;" u2="&#xae;" k="18" />
    <hkern u1="&#xc0;" u2="v" k="14" />
    <hkern u1="&#xc0;" u2="f" k="8" />
    <hkern u1="&#xc0;" u2="\" k="42" />
    <hkern u1="&#xc0;" u2="V" k="27" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc1;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc1;" u2="&#xae;" k="18" />
    <hkern u1="&#xc1;" u2="v" k="14" />
    <hkern u1="&#xc1;" u2="f" k="8" />
    <hkern u1="&#xc1;" u2="\" k="42" />
    <hkern u1="&#xc1;" u2="V" k="27" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc2;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc2;" u2="&#xae;" k="18" />
    <hkern u1="&#xc2;" u2="v" k="14" />
    <hkern u1="&#xc2;" u2="f" k="8" />
    <hkern u1="&#xc2;" u2="\" k="42" />
    <hkern u1="&#xc2;" u2="V" k="27" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc3;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc3;" u2="&#xae;" k="18" />
    <hkern u1="&#xc3;" u2="v" k="14" />
    <hkern u1="&#xc3;" u2="f" k="8" />
    <hkern u1="&#xc3;" u2="\" k="42" />
    <hkern u1="&#xc3;" u2="V" k="27" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc4;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc4;" u2="&#xae;" k="18" />
    <hkern u1="&#xc4;" u2="v" k="14" />
    <hkern u1="&#xc4;" u2="f" k="8" />
    <hkern u1="&#xc4;" u2="\" k="42" />
    <hkern u1="&#xc4;" u2="V" k="27" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="30" />
    <hkern u1="&#xc5;" u2="&#xf0;" k="5" />
    <hkern u1="&#xc5;" u2="&#xae;" k="18" />
    <hkern u1="&#xc5;" u2="v" k="14" />
    <hkern u1="&#xc5;" u2="f" k="8" />
    <hkern u1="&#xc5;" u2="\" k="42" />
    <hkern u1="&#xc5;" u2="V" k="27" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="14" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="27" />
    <hkern u1="&#xc6;" u2="&#x135;" k="-21" />
    <hkern u1="&#xc6;" u2="&#x12d;" k="-6" />
    <hkern u1="&#xc6;" u2="&#x129;" k="-22" />
    <hkern u1="&#xc6;" u2="&#xf0;" k="6" />
    <hkern u1="&#xc6;" u2="&#xef;" k="-32" />
    <hkern u1="&#xc6;" u2="&#xee;" k="-29" />
    <hkern u1="&#xc6;" u2="&#xec;" k="-42" />
    <hkern u1="&#xc7;" u2="&#x135;" k="-21" />
    <hkern u1="&#xc7;" u2="&#x12d;" k="-10" />
    <hkern u1="&#xc7;" u2="&#x129;" k="-24" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="6" />
    <hkern u1="&#xc7;" u2="&#xef;" k="-35" />
    <hkern u1="&#xc7;" u2="&#xee;" k="-29" />
    <hkern u1="&#xc7;" u2="&#xec;" k="-44" />
    <hkern u1="&#xc7;" u2="v" k="5" />
    <hkern u1="&#xc7;" u2="f" k="5" />
    <hkern u1="&#xc8;" u2="&#x135;" k="-21" />
    <hkern u1="&#xc8;" u2="&#x12d;" k="-6" />
    <hkern u1="&#xc8;" u2="&#x129;" k="-22" />
    <hkern u1="&#xc8;" u2="&#xf0;" k="6" />
    <hkern u1="&#xc8;" u2="&#xef;" k="-32" />
    <hkern u1="&#xc8;" u2="&#xee;" k="-29" />
    <hkern u1="&#xc8;" u2="&#xec;" k="-42" />
    <hkern u1="&#xc9;" u2="&#x135;" k="-21" />
    <hkern u1="&#xc9;" u2="&#x12d;" k="-6" />
    <hkern u1="&#xc9;" u2="&#x129;" k="-22" />
    <hkern u1="&#xc9;" u2="&#xf0;" k="6" />
    <hkern u1="&#xc9;" u2="&#xef;" k="-32" />
    <hkern u1="&#xc9;" u2="&#xee;" k="-29" />
    <hkern u1="&#xc9;" u2="&#xec;" k="-42" />
    <hkern u1="&#xca;" u2="&#x135;" k="-21" />
    <hkern u1="&#xca;" u2="&#x12d;" k="-6" />
    <hkern u1="&#xca;" u2="&#x129;" k="-22" />
    <hkern u1="&#xca;" u2="&#xf0;" k="6" />
    <hkern u1="&#xca;" u2="&#xef;" k="-32" />
    <hkern u1="&#xca;" u2="&#xee;" k="-29" />
    <hkern u1="&#xca;" u2="&#xec;" k="-42" />
    <hkern u1="&#xcb;" u2="&#x135;" k="-21" />
    <hkern u1="&#xcb;" u2="&#x12d;" k="-6" />
    <hkern u1="&#xcb;" u2="&#x129;" k="-22" />
    <hkern u1="&#xcb;" u2="&#xf0;" k="6" />
    <hkern u1="&#xcb;" u2="&#xef;" k="-32" />
    <hkern u1="&#xcb;" u2="&#xee;" k="-29" />
    <hkern u1="&#xcb;" u2="&#xec;" k="-42" />
    <hkern u1="&#xcc;" u2="&#xf0;" k="8" />
    <hkern u1="&#xcc;" u2="&#xec;" k="-8" />
    <hkern u1="&#xcc;" u2="f" k="6" />
    <hkern u1="&#xcd;" u2="&#xf0;" k="8" />
    <hkern u1="&#xcd;" u2="&#xec;" k="-8" />
    <hkern u1="&#xcd;" u2="f" k="6" />
    <hkern u1="&#xce;" g2="braceright.cap" k="-37" />
    <hkern u1="&#xce;" g2="bracketright.cap" k="-40" />
    <hkern u1="&#xce;" g2="parenright.cap" k="-41" />
    <hkern u1="&#xce;" u2="&#xf0;" k="8" />
    <hkern u1="&#xce;" u2="&#xec;" k="-8" />
    <hkern u1="&#xce;" u2="f" k="6" />
    <hkern u1="&#xcf;" g2="braceright.cap" k="-19" />
    <hkern u1="&#xcf;" g2="bracketright.cap" k="-22" />
    <hkern u1="&#xcf;" g2="parenright.cap" k="-30" />
    <hkern u1="&#xcf;" u2="&#xf0;" k="8" />
    <hkern u1="&#xcf;" u2="&#xec;" k="-8" />
    <hkern u1="&#xcf;" u2="f" k="6" />
    <hkern u1="&#xd0;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd0;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd0;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd0;" u2="]" k="19" />
    <hkern u1="&#xd0;" u2="\" k="18" />
    <hkern u1="&#xd0;" u2="X" k="18" />
    <hkern u1="&#xd0;" u2="V" k="10" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="15" />
    <hkern u1="&#xd0;" u2="&#x29;" k="13" />
    <hkern u1="&#xd1;" u2="&#xf0;" k="8" />
    <hkern u1="&#xd1;" u2="&#xec;" k="-8" />
    <hkern u1="&#xd1;" u2="f" k="6" />
    <hkern u1="&#xd2;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd2;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd2;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd2;" u2="]" k="20" />
    <hkern u1="&#xd2;" u2="\" k="19" />
    <hkern u1="&#xd2;" u2="X" k="17" />
    <hkern u1="&#xd2;" u2="V" k="10" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd2;" u2="&#x29;" k="13" />
    <hkern u1="&#xd3;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd3;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd3;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd3;" u2="]" k="20" />
    <hkern u1="&#xd3;" u2="\" k="19" />
    <hkern u1="&#xd3;" u2="X" k="17" />
    <hkern u1="&#xd3;" u2="V" k="10" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd3;" u2="&#x29;" k="13" />
    <hkern u1="&#xd4;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd4;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd4;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd4;" u2="]" k="20" />
    <hkern u1="&#xd4;" u2="\" k="19" />
    <hkern u1="&#xd4;" u2="X" k="17" />
    <hkern u1="&#xd4;" u2="V" k="10" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd4;" u2="&#x29;" k="13" />
    <hkern u1="&#xd5;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd5;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd5;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd5;" u2="]" k="20" />
    <hkern u1="&#xd5;" u2="\" k="19" />
    <hkern u1="&#xd5;" u2="X" k="17" />
    <hkern u1="&#xd5;" u2="V" k="10" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd5;" u2="&#x29;" k="13" />
    <hkern u1="&#xd6;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd6;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd6;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd6;" u2="]" k="20" />
    <hkern u1="&#xd6;" u2="\" k="19" />
    <hkern u1="&#xd6;" u2="X" k="17" />
    <hkern u1="&#xd6;" u2="V" k="10" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd6;" u2="&#x29;" k="13" />
    <hkern u1="&#xd8;" g2="braceright.cap" k="13" />
    <hkern u1="&#xd8;" g2="bracketright.cap" k="20" />
    <hkern u1="&#xd8;" g2="parenright.cap" k="14" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="13" />
    <hkern u1="&#xd8;" u2="&#x7d;" k="12" />
    <hkern u1="&#xd8;" u2="]" k="20" />
    <hkern u1="&#xd8;" u2="\" k="19" />
    <hkern u1="&#xd8;" u2="X" k="17" />
    <hkern u1="&#xd8;" u2="V" k="10" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="14" />
    <hkern u1="&#xd8;" u2="&#x29;" k="13" />
    <hkern u1="&#xd9;" u2="&#xf0;" k="8" />
    <hkern u1="&#xd9;" u2="&#xec;" k="-14" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="7" />
    <hkern u1="&#xd9;" u2="f" k="5" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="16" />
    <hkern u1="&#xda;" u2="&#xf0;" k="8" />
    <hkern u1="&#xda;" u2="&#xec;" k="-14" />
    <hkern u1="&#xda;" u2="&#xc6;" k="7" />
    <hkern u1="&#xda;" u2="f" k="5" />
    <hkern u1="&#xda;" u2="&#x2f;" k="16" />
    <hkern u1="&#xdb;" u2="&#xf0;" k="8" />
    <hkern u1="&#xdb;" u2="&#xec;" k="-14" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="7" />
    <hkern u1="&#xdb;" u2="f" k="5" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="16" />
    <hkern u1="&#xdc;" u2="&#xf0;" k="8" />
    <hkern u1="&#xdc;" u2="&#xec;" k="-14" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="7" />
    <hkern u1="&#xdc;" u2="f" k="5" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="16" />
    <hkern u1="&#xdd;" u2="&#x1ef9;" k="24" />
    <hkern u1="&#xdd;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#xdd;" u2="&#x159;" k="26" />
    <hkern u1="&#xdd;" u2="&#x155;" k="32" />
    <hkern u1="&#xdd;" u2="&#x151;" k="48" />
    <hkern u1="&#xdd;" u2="&#x142;" k="6" />
    <hkern u1="&#xdd;" u2="&#x135;" k="-15" />
    <hkern u1="&#xdd;" u2="&#x131;" k="59" />
    <hkern u1="&#xdd;" u2="&#x12d;" k="-44" />
    <hkern u1="&#xdd;" u2="&#x12b;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x129;" k="-49" />
    <hkern u1="&#xdd;" u2="&#x103;" k="56" />
    <hkern u1="&#xdd;" u2="&#xff;" k="24" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="26" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-70" />
    <hkern u1="&#xdd;" u2="&#xee;" k="-21" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-62" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="61" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="43" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="34" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="14" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="59" />
    <hkern u1="&#xdd;" u2="&#xae;" k="19" />
    <hkern u1="&#xdd;" u2="x" k="34" />
    <hkern u1="&#xdd;" u2="v" k="33" />
    <hkern u1="&#xdd;" u2="f" k="23" />
    <hkern u1="&#xdd;" u2="&#x40;" k="35" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="65" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-5" />
    <hkern u1="&#xdd;" u2="&#x26;" k="28" />
    <hkern u1="&#xde;" g2="braceright.cap" k="17" />
    <hkern u1="&#xde;" g2="bracketright.cap" k="29" />
    <hkern u1="&#xde;" g2="parenright.cap" k="18" />
    <hkern u1="&#xde;" u2="&#x2026;" k="19" />
    <hkern u1="&#xde;" u2="&#x201e;" k="19" />
    <hkern u1="&#xde;" u2="&#x201a;" k="19" />
    <hkern u1="&#xde;" u2="&#x1ef8;" k="35" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="35" />
    <hkern u1="&#xde;" u2="&#x1eb0;" k="14" />
    <hkern u1="&#xde;" u2="&#x1e84;" k="5" />
    <hkern u1="&#xde;" u2="&#x1e82;" k="5" />
    <hkern u1="&#xde;" u2="&#x1e80;" k="5" />
    <hkern u1="&#xde;" u2="&#x21a;" k="19" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="14" />
    <hkern u1="&#xde;" u2="&#x1fa;" k="14" />
    <hkern u1="&#xde;" u2="&#x17d;" k="9" />
    <hkern u1="&#xde;" u2="&#x17b;" k="9" />
    <hkern u1="&#xde;" u2="&#x179;" k="9" />
    <hkern u1="&#xde;" u2="&#x178;" k="35" />
    <hkern u1="&#xde;" u2="&#x176;" k="35" />
    <hkern u1="&#xde;" u2="&#x174;" k="5" />
    <hkern u1="&#xde;" u2="&#x166;" k="19" />
    <hkern u1="&#xde;" u2="&#x164;" k="19" />
    <hkern u1="&#xde;" u2="&#x134;" k="7" />
    <hkern u1="&#xde;" u2="&#x104;" k="14" />
    <hkern u1="&#xde;" u2="&#x102;" k="14" />
    <hkern u1="&#xde;" u2="&#x100;" k="14" />
    <hkern u1="&#xde;" u2="&#xdd;" k="35" />
    <hkern u1="&#xde;" u2="&#xc6;" k="17" />
    <hkern u1="&#xde;" u2="&#xc5;" k="14" />
    <hkern u1="&#xde;" u2="&#xc4;" k="14" />
    <hkern u1="&#xde;" u2="&#xc3;" k="14" />
    <hkern u1="&#xde;" u2="&#xc2;" k="14" />
    <hkern u1="&#xde;" u2="&#xc1;" k="14" />
    <hkern u1="&#xde;" u2="&#xc0;" k="14" />
    <hkern u1="&#xde;" u2="&#x7d;" k="16" />
    <hkern u1="&#xde;" u2="]" k="29" />
    <hkern u1="&#xde;" u2="\" k="24" />
    <hkern u1="&#xde;" u2="Z" k="9" />
    <hkern u1="&#xde;" u2="Y" k="35" />
    <hkern u1="&#xde;" u2="X" k="33" />
    <hkern u1="&#xde;" u2="W" k="5" />
    <hkern u1="&#xde;" u2="V" k="13" />
    <hkern u1="&#xde;" u2="T" k="19" />
    <hkern u1="&#xde;" u2="J" k="7" />
    <hkern u1="&#xde;" u2="A" k="14" />
    <hkern u1="&#xde;" u2="&#x2f;" k="23" />
    <hkern u1="&#xde;" u2="&#x2e;" k="19" />
    <hkern u1="&#xde;" u2="&#x2c;" k="19" />
    <hkern u1="&#xde;" u2="&#x29;" k="17" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="9" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="12" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="14" />
    <hkern u1="&#xdf;" u2="&#x2019;" k="12" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="14" />
    <hkern u1="&#xdf;" u2="&#x1ef9;" k="13" />
    <hkern u1="&#xdf;" u2="&#x1ef8;" k="36" />
    <hkern u1="&#xdf;" u2="&#x1ef3;" k="13" />
    <hkern u1="&#xdf;" u2="&#x1ef2;" k="36" />
    <hkern u1="&#xdf;" u2="&#x1e85;" k="6" />
    <hkern u1="&#xdf;" u2="&#x1e84;" k="14" />
    <hkern u1="&#xdf;" u2="&#x1e83;" k="6" />
    <hkern u1="&#xdf;" u2="&#x1e82;" k="14" />
    <hkern u1="&#xdf;" u2="&#x1e81;" k="6" />
    <hkern u1="&#xdf;" u2="&#x1e80;" k="14" />
    <hkern u1="&#xdf;" u2="&#x21b;" k="5" />
    <hkern u1="&#xdf;" u2="&#x21a;" k="18" />
    <hkern u1="&#xdf;" u2="&#x178;" k="36" />
    <hkern u1="&#xdf;" u2="&#x177;" k="13" />
    <hkern u1="&#xdf;" u2="&#x176;" k="36" />
    <hkern u1="&#xdf;" u2="&#x175;" k="6" />
    <hkern u1="&#xdf;" u2="&#x174;" k="14" />
    <hkern u1="&#xdf;" u2="&#x172;" k="6" />
    <hkern u1="&#xdf;" u2="&#x170;" k="6" />
    <hkern u1="&#xdf;" u2="&#x16e;" k="6" />
    <hkern u1="&#xdf;" u2="&#x16c;" k="6" />
    <hkern u1="&#xdf;" u2="&#x16a;" k="6" />
    <hkern u1="&#xdf;" u2="&#x168;" k="6" />
    <hkern u1="&#xdf;" u2="&#x167;" k="5" />
    <hkern u1="&#xdf;" u2="&#x166;" k="18" />
    <hkern u1="&#xdf;" u2="&#x165;" k="5" />
    <hkern u1="&#xdf;" u2="&#x164;" k="18" />
    <hkern u1="&#xdf;" u2="&#x123;" k="4" />
    <hkern u1="&#xdf;" u2="&#x121;" k="4" />
    <hkern u1="&#xdf;" u2="&#x11f;" k="4" />
    <hkern u1="&#xdf;" u2="&#x11d;" k="4" />
    <hkern u1="&#xdf;" u2="&#xff;" k="13" />
    <hkern u1="&#xdf;" u2="&#xfd;" k="13" />
    <hkern u1="&#xdf;" u2="&#xdd;" k="36" />
    <hkern u1="&#xdf;" u2="&#xdc;" k="6" />
    <hkern u1="&#xdf;" u2="&#xdb;" k="6" />
    <hkern u1="&#xdf;" u2="&#xda;" k="6" />
    <hkern u1="&#xdf;" u2="&#xd9;" k="6" />
    <hkern u1="&#xdf;" u2="&#xae;" k="12" />
    <hkern u1="&#xdf;" u2="y" k="13" />
    <hkern u1="&#xdf;" u2="x" k="4" />
    <hkern u1="&#xdf;" u2="w" k="6" />
    <hkern u1="&#xdf;" u2="v" k="11" />
    <hkern u1="&#xdf;" u2="t" k="5" />
    <hkern u1="&#xdf;" u2="g" k="4" />
    <hkern u1="&#xdf;" u2="f" k="6" />
    <hkern u1="&#xdf;" u2="]" k="10" />
    <hkern u1="&#xdf;" u2="\" k="21" />
    <hkern u1="&#xdf;" u2="Y" k="36" />
    <hkern u1="&#xdf;" u2="X" k="5" />
    <hkern u1="&#xdf;" u2="W" k="14" />
    <hkern u1="&#xdf;" u2="V" k="21" />
    <hkern u1="&#xdf;" u2="U" k="6" />
    <hkern u1="&#xdf;" u2="T" k="18" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="12" />
    <hkern u1="&#xdf;" u2="&#x27;" k="11" />
    <hkern u1="&#xdf;" u2="&#x22;" k="11" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe0;" u2="v" k="6" />
    <hkern u1="&#xe0;" u2="\" k="43" />
    <hkern u1="&#xe0;" u2="V" k="28" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe1;" u2="v" k="6" />
    <hkern u1="&#xe1;" u2="\" k="43" />
    <hkern u1="&#xe1;" u2="V" k="28" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe2;" u2="v" k="6" />
    <hkern u1="&#xe2;" u2="\" k="43" />
    <hkern u1="&#xe2;" u2="V" k="28" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe3;" u2="v" k="6" />
    <hkern u1="&#xe3;" u2="\" k="43" />
    <hkern u1="&#xe3;" u2="V" k="28" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe4;" u2="v" k="6" />
    <hkern u1="&#xe4;" u2="\" k="43" />
    <hkern u1="&#xe4;" u2="V" k="28" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="15" />
    <hkern u1="&#xe5;" u2="v" k="6" />
    <hkern u1="&#xe5;" u2="\" k="43" />
    <hkern u1="&#xe5;" u2="V" k="28" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="13" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="8" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="12" />
    <hkern u1="&#xe6;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe6;" u2="v" k="6" />
    <hkern u1="&#xe6;" u2="\" k="38" />
    <hkern u1="&#xe6;" u2="X" k="5" />
    <hkern u1="&#xe6;" u2="V" k="25" />
    <hkern u1="&#xe6;" u2="&#x3f;" k="11" />
    <hkern u1="&#xe6;" u2="&#x29;" k="12" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="7" />
    <hkern u1="&#xe7;" u2="\" k="24" />
    <hkern u1="&#xe7;" u2="V" k="12" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="12" />
    <hkern u1="&#xe8;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe8;" u2="v" k="6" />
    <hkern u1="&#xe8;" u2="\" k="38" />
    <hkern u1="&#xe8;" u2="X" k="5" />
    <hkern u1="&#xe8;" u2="V" k="25" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="11" />
    <hkern u1="&#xe8;" u2="&#x29;" k="12" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="12" />
    <hkern u1="&#xe9;" u2="&#xc6;" k="5" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="10" />
    <hkern u1="&#xe9;" u2="v" k="6" />
    <hkern u1="&#xe9;" u2="\" k="38" />
    <hkern u1="&#xe9;" u2="X" k="5" />
    <hkern u1="&#xe9;" u2="V" k="25" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="11" />
    <hkern u1="&#xe9;" u2="&#x29;" k="12" />
    <hkern u1="&#xea;" u2="&#x2122;" k="12" />
    <hkern u1="&#xea;" u2="&#xc6;" k="5" />
    <hkern u1="&#xea;" u2="&#x7d;" k="10" />
    <hkern u1="&#xea;" u2="v" k="6" />
    <hkern u1="&#xea;" u2="\" k="38" />
    <hkern u1="&#xea;" u2="X" k="5" />
    <hkern u1="&#xea;" u2="V" k="25" />
    <hkern u1="&#xea;" u2="&#x3f;" k="11" />
    <hkern u1="&#xea;" u2="&#x29;" k="12" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="12" />
    <hkern u1="&#xeb;" u2="&#xc6;" k="5" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="10" />
    <hkern u1="&#xeb;" u2="v" k="6" />
    <hkern u1="&#xeb;" u2="\" k="38" />
    <hkern u1="&#xeb;" u2="X" k="5" />
    <hkern u1="&#xeb;" u2="V" k="25" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="11" />
    <hkern u1="&#xeb;" u2="&#x29;" k="12" />
    <hkern u1="&#xec;" u2="&#xef;" k="-8" />
    <hkern u1="&#xec;" u2="&#xec;" k="-18" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-36" />
    <hkern u1="&#xed;" u2="&#x201d;" k="-8" />
    <hkern u1="&#xed;" u2="&#x2019;" k="-8" />
    <hkern u1="&#xed;" u2="&#x165;" k="-13" />
    <hkern u1="&#xed;" u2="&#x159;" k="-48" />
    <hkern u1="&#xed;" u2="&#x142;" k="-18" />
    <hkern u1="&#xed;" u2="&#x13e;" k="-18" />
    <hkern u1="&#xed;" u2="&#x13c;" k="-18" />
    <hkern u1="&#xed;" u2="&#x13a;" k="-18" />
    <hkern u1="&#xed;" u2="&#x137;" k="-24" />
    <hkern u1="&#xed;" u2="&#x135;" k="-24" />
    <hkern u1="&#xed;" u2="&#x131;" k="-24" />
    <hkern u1="&#xed;" u2="&#x12f;" k="-24" />
    <hkern u1="&#xed;" u2="&#x12d;" k="-24" />
    <hkern u1="&#xed;" u2="&#x12b;" k="-24" />
    <hkern u1="&#xed;" u2="&#x129;" k="-24" />
    <hkern u1="&#xed;" u2="&#x127;" k="-24" />
    <hkern u1="&#xed;" u2="&#x125;" k="-24" />
    <hkern u1="&#xed;" u2="&#xfe;" k="-25" />
    <hkern u1="&#xed;" u2="&#xef;" k="-8" />
    <hkern u1="&#xed;" u2="&#xee;" k="-24" />
    <hkern u1="&#xed;" u2="&#xed;" k="-24" />
    <hkern u1="&#xed;" u2="&#xec;" k="-18" />
    <hkern u1="&#xed;" u2="&#xdf;" k="-24" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-51" />
    <hkern u1="&#xed;" u2="&#x7c;" k="-21" />
    <hkern u1="&#xed;" u2="l" k="-18" />
    <hkern u1="&#xed;" u2="k" k="-24" />
    <hkern u1="&#xed;" u2="j" k="-24" />
    <hkern u1="&#xed;" u2="i" k="-24" />
    <hkern u1="&#xed;" u2="h" k="-24" />
    <hkern u1="&#xed;" u2="b" k="-25" />
    <hkern u1="&#xed;" u2="]" k="-54" />
    <hkern u1="&#xed;" u2="\" k="-49" />
    <hkern u1="&#xed;" u2="&#x3f;" k="-59" />
    <hkern u1="&#xed;" u2="&#x2a;" k="-49" />
    <hkern u1="&#xed;" u2="&#x29;" k="-27" />
    <hkern u1="&#xed;" u2="&#x27;" k="-31" />
    <hkern u1="&#xed;" u2="&#x22;" k="-31" />
    <hkern u1="&#xed;" u2="&#x21;" k="-20" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-7" />
    <hkern u1="&#xee;" u2="&#xef;" k="-8" />
    <hkern u1="&#xee;" u2="&#xec;" k="-18" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-26" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-33" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-10" />
    <hkern u1="&#xef;" u2="&#xef;" k="-8" />
    <hkern u1="&#xef;" u2="&#xec;" k="-18" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-27" />
    <hkern u1="&#xef;" u2="]" k="-28" />
    <hkern u1="&#xef;" u2="\" k="-31" />
    <hkern u1="&#xef;" u2="&#x3f;" k="-34" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-45" />
    <hkern u1="&#xef;" u2="&#x29;" k="-28" />
    <hkern u1="&#xef;" u2="&#x27;" k="-6" />
    <hkern u1="&#xef;" u2="&#x22;" k="-6" />
    <hkern u1="&#xf0;" u2="&#x1ef9;" k="5" />
    <hkern u1="&#xf0;" u2="&#x1ef8;" k="33" />
    <hkern u1="&#xf0;" u2="&#x1ef3;" k="5" />
    <hkern u1="&#xf0;" u2="&#x1ef2;" k="33" />
    <hkern u1="&#xf0;" u2="&#x1eb0;" k="8" />
    <hkern u1="&#xf0;" u2="&#x1e84;" k="9" />
    <hkern u1="&#xf0;" u2="&#x1e82;" k="9" />
    <hkern u1="&#xf0;" u2="&#x1e80;" k="9" />
    <hkern u1="&#xf0;" u2="&#x21a;" k="21" />
    <hkern u1="&#xf0;" u2="&#x1fc;" k="8" />
    <hkern u1="&#xf0;" u2="&#x1fa;" k="8" />
    <hkern u1="&#xf0;" u2="&#x17d;" k="11" />
    <hkern u1="&#xf0;" u2="&#x17b;" k="11" />
    <hkern u1="&#xf0;" u2="&#x179;" k="11" />
    <hkern u1="&#xf0;" u2="&#x178;" k="33" />
    <hkern u1="&#xf0;" u2="&#x177;" k="5" />
    <hkern u1="&#xf0;" u2="&#x176;" k="33" />
    <hkern u1="&#xf0;" u2="&#x174;" k="9" />
    <hkern u1="&#xf0;" u2="&#x166;" k="21" />
    <hkern u1="&#xf0;" u2="&#x164;" k="21" />
    <hkern u1="&#xf0;" u2="&#x158;" k="5" />
    <hkern u1="&#xf0;" u2="&#x156;" k="5" />
    <hkern u1="&#xf0;" u2="&#x154;" k="5" />
    <hkern u1="&#xf0;" u2="&#x14a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x147;" k="5" />
    <hkern u1="&#xf0;" u2="&#x145;" k="5" />
    <hkern u1="&#xf0;" u2="&#x143;" k="5" />
    <hkern u1="&#xf0;" u2="&#x141;" k="5" />
    <hkern u1="&#xf0;" u2="&#x13d;" k="5" />
    <hkern u1="&#xf0;" u2="&#x13b;" k="5" />
    <hkern u1="&#xf0;" u2="&#x139;" k="5" />
    <hkern u1="&#xf0;" u2="&#x136;" k="5" />
    <hkern u1="&#xf0;" u2="&#x134;" k="14" />
    <hkern u1="&#xf0;" u2="&#x130;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12e;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12c;" k="5" />
    <hkern u1="&#xf0;" u2="&#x12a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x128;" k="5" />
    <hkern u1="&#xf0;" u2="&#x126;" k="5" />
    <hkern u1="&#xf0;" u2="&#x124;" k="5" />
    <hkern u1="&#xf0;" u2="&#x11a;" k="5" />
    <hkern u1="&#xf0;" u2="&#x118;" k="5" />
    <hkern u1="&#xf0;" u2="&#x116;" k="5" />
    <hkern u1="&#xf0;" u2="&#x114;" k="5" />
    <hkern u1="&#xf0;" u2="&#x112;" k="5" />
    <hkern u1="&#xf0;" u2="&#x110;" k="5" />
    <hkern u1="&#xf0;" u2="&#x10e;" k="5" />
    <hkern u1="&#xf0;" u2="&#x104;" k="8" />
    <hkern u1="&#xf0;" u2="&#x102;" k="8" />
    <hkern u1="&#xf0;" u2="&#x100;" k="8" />
    <hkern u1="&#xf0;" u2="&#xff;" k="5" />
    <hkern u1="&#xf0;" u2="&#xfd;" k="5" />
    <hkern u1="&#xf0;" u2="&#xde;" k="5" />
    <hkern u1="&#xf0;" u2="&#xdd;" k="33" />
    <hkern u1="&#xf0;" u2="&#xd1;" k="5" />
    <hkern u1="&#xf0;" u2="&#xd0;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcf;" k="5" />
    <hkern u1="&#xf0;" u2="&#xce;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcd;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcc;" k="5" />
    <hkern u1="&#xf0;" u2="&#xcb;" k="5" />
    <hkern u1="&#xf0;" u2="&#xca;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc9;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc8;" k="5" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf0;" u2="&#xc5;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc4;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc3;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc2;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc1;" k="8" />
    <hkern u1="&#xf0;" u2="&#xc0;" k="8" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="14" />
    <hkern u1="&#xf0;" u2="y" k="5" />
    <hkern u1="&#xf0;" u2="x" k="4" />
    <hkern u1="&#xf0;" u2="v" k="4" />
    <hkern u1="&#xf0;" u2="]" k="19" />
    <hkern u1="&#xf0;" u2="\" k="18" />
    <hkern u1="&#xf0;" u2="Z" k="11" />
    <hkern u1="&#xf0;" u2="Y" k="33" />
    <hkern u1="&#xf0;" u2="X" k="27" />
    <hkern u1="&#xf0;" u2="W" k="9" />
    <hkern u1="&#xf0;" u2="V" k="14" />
    <hkern u1="&#xf0;" u2="T" k="21" />
    <hkern u1="&#xf0;" u2="R" k="5" />
    <hkern u1="&#xf0;" u2="P" k="5" />
    <hkern u1="&#xf0;" u2="N" k="5" />
    <hkern u1="&#xf0;" u2="M" k="5" />
    <hkern u1="&#xf0;" u2="L" k="5" />
    <hkern u1="&#xf0;" u2="K" k="5" />
    <hkern u1="&#xf0;" u2="J" k="14" />
    <hkern u1="&#xf0;" u2="I" k="5" />
    <hkern u1="&#xf0;" u2="H" k="5" />
    <hkern u1="&#xf0;" u2="F" k="5" />
    <hkern u1="&#xf0;" u2="E" k="5" />
    <hkern u1="&#xf0;" u2="D" k="5" />
    <hkern u1="&#xf0;" u2="B" k="5" />
    <hkern u1="&#xf0;" u2="A" k="8" />
    <hkern u1="&#xf0;" u2="&#x2f;" k="12" />
    <hkern u1="&#xf0;" u2="&#x29;" k="16" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf1;" u2="v" k="4" />
    <hkern u1="&#xf1;" u2="\" k="40" />
    <hkern u1="&#xf1;" u2="V" k="27" />
    <hkern u1="&#xf1;" u2="&#x3f;" k="14" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf1;" u2="&#x29;" k="11" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf2;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf2;" u2="x" k="10" />
    <hkern u1="&#xf2;" u2="v" k="7" />
    <hkern u1="&#xf2;" u2="]" k="27" />
    <hkern u1="&#xf2;" u2="\" k="41" />
    <hkern u1="&#xf2;" u2="X" k="26" />
    <hkern u1="&#xf2;" u2="V" k="29" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf2;" u2="&#x29;" k="20" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf3;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf3;" u2="x" k="10" />
    <hkern u1="&#xf3;" u2="v" k="7" />
    <hkern u1="&#xf3;" u2="]" k="27" />
    <hkern u1="&#xf3;" u2="\" k="41" />
    <hkern u1="&#xf3;" u2="X" k="26" />
    <hkern u1="&#xf3;" u2="V" k="29" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf3;" u2="&#x29;" k="20" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf4;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf4;" u2="x" k="10" />
    <hkern u1="&#xf4;" u2="v" k="7" />
    <hkern u1="&#xf4;" u2="]" k="27" />
    <hkern u1="&#xf4;" u2="\" k="41" />
    <hkern u1="&#xf4;" u2="X" k="26" />
    <hkern u1="&#xf4;" u2="V" k="29" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf4;" u2="&#x29;" k="20" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf5;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf5;" u2="x" k="10" />
    <hkern u1="&#xf5;" u2="v" k="7" />
    <hkern u1="&#xf5;" u2="]" k="27" />
    <hkern u1="&#xf5;" u2="\" k="41" />
    <hkern u1="&#xf5;" u2="X" k="26" />
    <hkern u1="&#xf5;" u2="V" k="29" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf5;" u2="&#x29;" k="20" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf6;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf6;" u2="x" k="10" />
    <hkern u1="&#xf6;" u2="v" k="7" />
    <hkern u1="&#xf6;" u2="]" k="27" />
    <hkern u1="&#xf6;" u2="\" k="41" />
    <hkern u1="&#xf6;" u2="X" k="26" />
    <hkern u1="&#xf6;" u2="V" k="29" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf6;" u2="&#x29;" k="20" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="14" />
    <hkern u1="&#xf8;" u2="&#xc6;" k="7" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="19" />
    <hkern u1="&#xf8;" u2="x" k="10" />
    <hkern u1="&#xf8;" u2="v" k="7" />
    <hkern u1="&#xf8;" u2="]" k="27" />
    <hkern u1="&#xf8;" u2="\" k="41" />
    <hkern u1="&#xf8;" u2="X" k="26" />
    <hkern u1="&#xf8;" u2="V" k="29" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf8;" u2="&#x29;" k="20" />
    <hkern u1="&#xf9;" u2="&#x2122;" k="9" />
    <hkern u1="&#xf9;" u2="\" k="28" />
    <hkern u1="&#xf9;" u2="X" k="5" />
    <hkern u1="&#xf9;" u2="V" k="21" />
    <hkern u1="&#xf9;" u2="&#x29;" k="11" />
    <hkern u1="&#xfa;" u2="&#x2122;" k="9" />
    <hkern u1="&#xfa;" u2="\" k="28" />
    <hkern u1="&#xfa;" u2="X" k="5" />
    <hkern u1="&#xfa;" u2="V" k="21" />
    <hkern u1="&#xfa;" u2="&#x29;" k="11" />
    <hkern u1="&#xfb;" u2="&#x2122;" k="9" />
    <hkern u1="&#xfb;" u2="\" k="28" />
    <hkern u1="&#xfb;" u2="X" k="5" />
    <hkern u1="&#xfb;" u2="V" k="21" />
    <hkern u1="&#xfb;" u2="&#x29;" k="11" />
    <hkern u1="&#xfc;" u2="&#x2122;" k="9" />
    <hkern u1="&#xfc;" u2="\" k="28" />
    <hkern u1="&#xfc;" u2="X" k="5" />
    <hkern u1="&#xfc;" u2="V" k="21" />
    <hkern u1="&#xfc;" u2="&#x29;" k="11" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="13" />
    <hkern u1="&#xfd;" u2="&#xc6;" k="18" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="11" />
    <hkern u1="&#xfd;" u2="]" k="19" />
    <hkern u1="&#xfd;" u2="\" k="18" />
    <hkern u1="&#xfd;" u2="X" k="22" />
    <hkern u1="&#xfd;" u2="V" k="6" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="19" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="15" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="6" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="18" />
    <hkern u1="&#xfe;" u2="x" k="8" />
    <hkern u1="&#xfe;" u2="v" k="6" />
    <hkern u1="&#xfe;" u2="]" k="26" />
    <hkern u1="&#xfe;" u2="\" k="40" />
    <hkern u1="&#xfe;" u2="X" k="22" />
    <hkern u1="&#xfe;" u2="V" k="27" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="16" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="8" />
    <hkern u1="&#xfe;" u2="&#x29;" k="19" />
    <hkern u1="&#xff;" u2="&#xf0;" k="13" />
    <hkern u1="&#xff;" u2="&#xc6;" k="18" />
    <hkern u1="&#xff;" u2="&#x7d;" k="11" />
    <hkern u1="&#xff;" u2="]" k="19" />
    <hkern u1="&#xff;" u2="\" k="18" />
    <hkern u1="&#xff;" u2="X" k="22" />
    <hkern u1="&#xff;" u2="V" k="6" />
    <hkern u1="&#xff;" u2="&#x2f;" k="19" />
    <hkern u1="&#x100;" u2="&#x2122;" k="30" />
    <hkern u1="&#x100;" u2="&#xf0;" k="5" />
    <hkern u1="&#x100;" u2="&#xae;" k="18" />
    <hkern u1="&#x100;" u2="v" k="14" />
    <hkern u1="&#x100;" u2="f" k="8" />
    <hkern u1="&#x100;" u2="\" k="42" />
    <hkern u1="&#x100;" u2="V" k="27" />
    <hkern u1="&#x100;" u2="&#x3f;" k="14" />
    <hkern u1="&#x100;" u2="&#x2a;" k="27" />
    <hkern u1="&#x101;" u2="&#x2122;" k="15" />
    <hkern u1="&#x101;" u2="v" k="6" />
    <hkern u1="&#x101;" u2="\" k="43" />
    <hkern u1="&#x101;" u2="V" k="28" />
    <hkern u1="&#x101;" u2="&#x3f;" k="13" />
    <hkern u1="&#x101;" u2="&#x2a;" k="8" />
    <hkern u1="&#x102;" u2="&#x2122;" k="30" />
    <hkern u1="&#x102;" u2="&#xf0;" k="5" />
    <hkern u1="&#x102;" u2="&#xae;" k="18" />
    <hkern u1="&#x102;" u2="v" k="14" />
    <hkern u1="&#x102;" u2="f" k="8" />
    <hkern u1="&#x102;" u2="\" k="42" />
    <hkern u1="&#x102;" u2="V" k="27" />
    <hkern u1="&#x102;" u2="&#x3f;" k="14" />
    <hkern u1="&#x102;" u2="&#x2a;" k="27" />
    <hkern u1="&#x103;" u2="&#x2122;" k="15" />
    <hkern u1="&#x103;" u2="v" k="6" />
    <hkern u1="&#x103;" u2="\" k="43" />
    <hkern u1="&#x103;" u2="V" k="28" />
    <hkern u1="&#x103;" u2="&#x3f;" k="13" />
    <hkern u1="&#x103;" u2="&#x2a;" k="8" />
    <hkern u1="&#x104;" u2="&#x2122;" k="30" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-15" />
    <hkern u1="&#x104;" u2="&#xf0;" k="5" />
    <hkern u1="&#x104;" u2="&#xae;" k="18" />
    <hkern u1="&#x104;" u2="v" k="14" />
    <hkern u1="&#x104;" u2="j" k="-44" />
    <hkern u1="&#x104;" u2="f" k="8" />
    <hkern u1="&#x104;" u2="\" k="42" />
    <hkern u1="&#x104;" u2="V" k="27" />
    <hkern u1="&#x104;" u2="&#x3f;" k="14" />
    <hkern u1="&#x104;" u2="&#x2a;" k="27" />
    <hkern u1="&#x105;" u2="&#x2122;" k="15" />
    <hkern u1="&#x105;" u2="v" k="6" />
    <hkern u1="&#x105;" u2="j" k="-16" />
    <hkern u1="&#x105;" u2="\" k="43" />
    <hkern u1="&#x105;" u2="V" k="28" />
    <hkern u1="&#x105;" u2="&#x3f;" k="13" />
    <hkern u1="&#x105;" u2="&#x2a;" k="8" />
    <hkern u1="&#x106;" u2="&#x135;" k="-21" />
    <hkern u1="&#x106;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x106;" u2="&#x129;" k="-24" />
    <hkern u1="&#x106;" u2="&#xf0;" k="6" />
    <hkern u1="&#x106;" u2="&#xef;" k="-35" />
    <hkern u1="&#x106;" u2="&#xee;" k="-29" />
    <hkern u1="&#x106;" u2="&#xec;" k="-44" />
    <hkern u1="&#x106;" u2="v" k="5" />
    <hkern u1="&#x106;" u2="f" k="5" />
    <hkern u1="&#x107;" u2="&#xf0;" k="7" />
    <hkern u1="&#x107;" u2="\" k="24" />
    <hkern u1="&#x107;" u2="V" k="12" />
    <hkern u1="&#x108;" u2="&#x135;" k="-21" />
    <hkern u1="&#x108;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x108;" u2="&#x129;" k="-24" />
    <hkern u1="&#x108;" u2="&#xf0;" k="6" />
    <hkern u1="&#x108;" u2="&#xef;" k="-35" />
    <hkern u1="&#x108;" u2="&#xee;" k="-29" />
    <hkern u1="&#x108;" u2="&#xec;" k="-44" />
    <hkern u1="&#x108;" u2="v" k="5" />
    <hkern u1="&#x108;" u2="f" k="5" />
    <hkern u1="&#x109;" u2="&#xf0;" k="7" />
    <hkern u1="&#x109;" u2="\" k="24" />
    <hkern u1="&#x109;" u2="V" k="12" />
    <hkern u1="&#x10a;" u2="&#x135;" k="-21" />
    <hkern u1="&#x10a;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x10a;" u2="&#x129;" k="-24" />
    <hkern u1="&#x10a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x10a;" u2="&#xef;" k="-35" />
    <hkern u1="&#x10a;" u2="&#xee;" k="-29" />
    <hkern u1="&#x10a;" u2="&#xec;" k="-44" />
    <hkern u1="&#x10a;" u2="v" k="5" />
    <hkern u1="&#x10a;" u2="f" k="5" />
    <hkern u1="&#x10b;" u2="&#xf0;" k="7" />
    <hkern u1="&#x10b;" u2="\" k="24" />
    <hkern u1="&#x10b;" u2="V" k="12" />
    <hkern u1="&#x10c;" u2="&#x135;" k="-21" />
    <hkern u1="&#x10c;" u2="&#x12d;" k="-10" />
    <hkern u1="&#x10c;" u2="&#x129;" k="-24" />
    <hkern u1="&#x10c;" u2="&#xf0;" k="6" />
    <hkern u1="&#x10c;" u2="&#xef;" k="-35" />
    <hkern u1="&#x10c;" u2="&#xee;" k="-29" />
    <hkern u1="&#x10c;" u2="&#xec;" k="-44" />
    <hkern u1="&#x10c;" u2="v" k="5" />
    <hkern u1="&#x10c;" u2="f" k="5" />
    <hkern u1="&#x10d;" u2="&#xf0;" k="7" />
    <hkern u1="&#x10d;" u2="\" k="24" />
    <hkern u1="&#x10d;" u2="V" k="12" />
    <hkern u1="&#x10e;" g2="braceright.cap" k="13" />
    <hkern u1="&#x10e;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x10e;" g2="parenright.cap" k="14" />
    <hkern u1="&#x10e;" u2="&#xc6;" k="13" />
    <hkern u1="&#x10e;" u2="&#x7d;" k="12" />
    <hkern u1="&#x10e;" u2="]" k="19" />
    <hkern u1="&#x10e;" u2="\" k="18" />
    <hkern u1="&#x10e;" u2="X" k="18" />
    <hkern u1="&#x10e;" u2="V" k="10" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="15" />
    <hkern u1="&#x10e;" u2="&#x29;" k="13" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-22" />
    <hkern u1="&#x10f;" u2="&#x161;" k="-17" />
    <hkern u1="&#x10f;" u2="&#x10d;" k="-13" />
    <hkern u1="&#x10f;" u2="&#xf0;" k="-21" />
    <hkern u1="&#x10f;" u2="&#xe1;" k="-9" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-23" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-45" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-13" />
    <hkern u1="&#x10f;" u2="x" k="-23" />
    <hkern u1="&#x10f;" u2="v" k="-22" />
    <hkern u1="&#x10f;" u2="f" k="-5" />
    <hkern u1="&#x10f;" u2="]" k="-46" />
    <hkern u1="&#x10f;" u2="\" k="-46" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-47" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="35" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-48" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-29" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-13" />
    <hkern u1="&#x110;" g2="braceright.cap" k="13" />
    <hkern u1="&#x110;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x110;" g2="parenright.cap" k="14" />
    <hkern u1="&#x110;" u2="&#xc6;" k="13" />
    <hkern u1="&#x110;" u2="&#x7d;" k="12" />
    <hkern u1="&#x110;" u2="]" k="19" />
    <hkern u1="&#x110;" u2="\" k="18" />
    <hkern u1="&#x110;" u2="X" k="18" />
    <hkern u1="&#x110;" u2="V" k="10" />
    <hkern u1="&#x110;" u2="&#x2f;" k="15" />
    <hkern u1="&#x110;" u2="&#x29;" k="13" />
    <hkern u1="&#x111;" u2="&#xef;" k="-8" />
    <hkern u1="&#x111;" u2="&#xec;" k="-18" />
    <hkern u1="&#x112;" u2="&#x135;" k="-21" />
    <hkern u1="&#x112;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x112;" u2="&#x129;" k="-22" />
    <hkern u1="&#x112;" u2="&#xf0;" k="6" />
    <hkern u1="&#x112;" u2="&#xef;" k="-32" />
    <hkern u1="&#x112;" u2="&#xee;" k="-29" />
    <hkern u1="&#x112;" u2="&#xec;" k="-42" />
    <hkern u1="&#x113;" u2="&#x2122;" k="12" />
    <hkern u1="&#x113;" u2="&#xc6;" k="5" />
    <hkern u1="&#x113;" u2="&#x7d;" k="10" />
    <hkern u1="&#x113;" u2="v" k="6" />
    <hkern u1="&#x113;" u2="\" k="38" />
    <hkern u1="&#x113;" u2="X" k="5" />
    <hkern u1="&#x113;" u2="V" k="25" />
    <hkern u1="&#x113;" u2="&#x3f;" k="11" />
    <hkern u1="&#x113;" u2="&#x29;" k="12" />
    <hkern u1="&#x114;" u2="&#x135;" k="-21" />
    <hkern u1="&#x114;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x114;" u2="&#x129;" k="-22" />
    <hkern u1="&#x114;" u2="&#xf0;" k="6" />
    <hkern u1="&#x114;" u2="&#xef;" k="-32" />
    <hkern u1="&#x114;" u2="&#xee;" k="-29" />
    <hkern u1="&#x114;" u2="&#xec;" k="-42" />
    <hkern u1="&#x115;" u2="&#x2122;" k="12" />
    <hkern u1="&#x115;" u2="&#xc6;" k="5" />
    <hkern u1="&#x115;" u2="&#x7d;" k="10" />
    <hkern u1="&#x115;" u2="v" k="6" />
    <hkern u1="&#x115;" u2="\" k="38" />
    <hkern u1="&#x115;" u2="X" k="5" />
    <hkern u1="&#x115;" u2="V" k="25" />
    <hkern u1="&#x115;" u2="&#x3f;" k="11" />
    <hkern u1="&#x115;" u2="&#x29;" k="12" />
    <hkern u1="&#x116;" u2="&#x135;" k="-21" />
    <hkern u1="&#x116;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x116;" u2="&#x129;" k="-22" />
    <hkern u1="&#x116;" u2="&#xf0;" k="6" />
    <hkern u1="&#x116;" u2="&#xef;" k="-32" />
    <hkern u1="&#x116;" u2="&#xee;" k="-29" />
    <hkern u1="&#x116;" u2="&#xec;" k="-42" />
    <hkern u1="&#x117;" u2="&#x2122;" k="12" />
    <hkern u1="&#x117;" u2="&#xc6;" k="5" />
    <hkern u1="&#x117;" u2="&#x7d;" k="10" />
    <hkern u1="&#x117;" u2="v" k="6" />
    <hkern u1="&#x117;" u2="\" k="38" />
    <hkern u1="&#x117;" u2="X" k="5" />
    <hkern u1="&#x117;" u2="V" k="25" />
    <hkern u1="&#x117;" u2="&#x3f;" k="11" />
    <hkern u1="&#x117;" u2="&#x29;" k="12" />
    <hkern u1="&#x118;" u2="&#x135;" k="-21" />
    <hkern u1="&#x118;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x118;" u2="&#x129;" k="-22" />
    <hkern u1="&#x118;" u2="&#xf0;" k="6" />
    <hkern u1="&#x118;" u2="&#xef;" k="-32" />
    <hkern u1="&#x118;" u2="&#xee;" k="-29" />
    <hkern u1="&#x118;" u2="&#xec;" k="-42" />
    <hkern u1="&#x118;" u2="j" k="-11" />
    <hkern u1="&#x119;" u2="&#x2122;" k="12" />
    <hkern u1="&#x119;" u2="&#xc6;" k="5" />
    <hkern u1="&#x119;" u2="&#x7d;" k="10" />
    <hkern u1="&#x119;" u2="v" k="6" />
    <hkern u1="&#x119;" u2="\" k="38" />
    <hkern u1="&#x119;" u2="X" k="5" />
    <hkern u1="&#x119;" u2="V" k="25" />
    <hkern u1="&#x119;" u2="&#x3f;" k="11" />
    <hkern u1="&#x119;" u2="&#x29;" k="12" />
    <hkern u1="&#x11a;" u2="&#x135;" k="-21" />
    <hkern u1="&#x11a;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x11a;" u2="&#x129;" k="-22" />
    <hkern u1="&#x11a;" u2="&#xf0;" k="6" />
    <hkern u1="&#x11a;" u2="&#xef;" k="-32" />
    <hkern u1="&#x11a;" u2="&#xee;" k="-29" />
    <hkern u1="&#x11a;" u2="&#xec;" k="-42" />
    <hkern u1="&#x11b;" u2="&#x2122;" k="12" />
    <hkern u1="&#x11b;" u2="&#xc6;" k="5" />
    <hkern u1="&#x11b;" u2="&#x7d;" k="10" />
    <hkern u1="&#x11b;" u2="v" k="6" />
    <hkern u1="&#x11b;" u2="\" k="38" />
    <hkern u1="&#x11b;" u2="X" k="5" />
    <hkern u1="&#x11b;" u2="V" k="25" />
    <hkern u1="&#x11b;" u2="&#x3f;" k="11" />
    <hkern u1="&#x11b;" u2="&#x29;" k="12" />
    <hkern u1="&#x11c;" u2="&#xef;" k="-15" />
    <hkern u1="&#x11c;" u2="&#xee;" k="-9" />
    <hkern u1="&#x11c;" u2="&#xec;" k="-25" />
    <hkern u1="&#x11c;" u2="v" k="5" />
    <hkern u1="&#x11c;" u2="f" k="6" />
    <hkern u1="&#x11c;" u2="\" k="10" />
    <hkern u1="&#x11c;" u2="V" k="8" />
    <hkern u1="&#x11d;" u2="&#x135;" k="-24" />
    <hkern u1="&#x11d;" u2="&#xf0;" k="6" />
    <hkern u1="&#x11d;" u2="j" k="-24" />
    <hkern u1="&#x11d;" u2="\" k="15" />
    <hkern u1="&#x11d;" u2="V" k="5" />
    <hkern u1="&#x11e;" u2="&#xef;" k="-15" />
    <hkern u1="&#x11e;" u2="&#xee;" k="-9" />
    <hkern u1="&#x11e;" u2="&#xec;" k="-25" />
    <hkern u1="&#x11e;" u2="v" k="5" />
    <hkern u1="&#x11e;" u2="f" k="6" />
    <hkern u1="&#x11e;" u2="\" k="10" />
    <hkern u1="&#x11e;" u2="V" k="8" />
    <hkern u1="&#x11f;" u2="&#x135;" k="-24" />
    <hkern u1="&#x11f;" u2="&#xf0;" k="6" />
    <hkern u1="&#x11f;" u2="j" k="-24" />
    <hkern u1="&#x11f;" u2="\" k="15" />
    <hkern u1="&#x11f;" u2="V" k="5" />
    <hkern u1="&#x120;" u2="&#xef;" k="-15" />
    <hkern u1="&#x120;" u2="&#xee;" k="-9" />
    <hkern u1="&#x120;" u2="&#xec;" k="-25" />
    <hkern u1="&#x120;" u2="v" k="5" />
    <hkern u1="&#x120;" u2="f" k="6" />
    <hkern u1="&#x120;" u2="\" k="10" />
    <hkern u1="&#x120;" u2="V" k="8" />
    <hkern u1="&#x121;" u2="&#x135;" k="-24" />
    <hkern u1="&#x121;" u2="&#xf0;" k="6" />
    <hkern u1="&#x121;" u2="j" k="-24" />
    <hkern u1="&#x121;" u2="\" k="15" />
    <hkern u1="&#x121;" u2="V" k="5" />
    <hkern u1="&#x122;" u2="&#xef;" k="-15" />
    <hkern u1="&#x122;" u2="&#xee;" k="-9" />
    <hkern u1="&#x122;" u2="&#xec;" k="-25" />
    <hkern u1="&#x122;" u2="v" k="5" />
    <hkern u1="&#x122;" u2="f" k="6" />
    <hkern u1="&#x122;" u2="\" k="10" />
    <hkern u1="&#x122;" u2="V" k="8" />
    <hkern u1="&#x123;" u2="&#x135;" k="-24" />
    <hkern u1="&#x123;" u2="&#xf0;" k="6" />
    <hkern u1="&#x123;" u2="j" k="-24" />
    <hkern u1="&#x123;" u2="\" k="15" />
    <hkern u1="&#x123;" u2="V" k="5" />
    <hkern u1="&#x124;" u2="&#xf0;" k="8" />
    <hkern u1="&#x124;" u2="&#xec;" k="-8" />
    <hkern u1="&#x124;" u2="f" k="6" />
    <hkern u1="&#x125;" u2="&#x2122;" k="14" />
    <hkern u1="&#x125;" u2="v" k="4" />
    <hkern u1="&#x125;" u2="\" k="40" />
    <hkern u1="&#x125;" u2="V" k="27" />
    <hkern u1="&#x125;" u2="&#x3f;" k="14" />
    <hkern u1="&#x125;" u2="&#x2a;" k="8" />
    <hkern u1="&#x125;" u2="&#x29;" k="11" />
    <hkern u1="&#x126;" u2="&#xf0;" k="8" />
    <hkern u1="&#x126;" u2="&#xec;" k="-8" />
    <hkern u1="&#x126;" u2="f" k="6" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-22" />
    <hkern u1="&#x127;" u2="&#x2122;" k="14" />
    <hkern u1="&#x127;" u2="v" k="4" />
    <hkern u1="&#x127;" u2="\" k="40" />
    <hkern u1="&#x127;" u2="V" k="27" />
    <hkern u1="&#x127;" u2="&#x3f;" k="14" />
    <hkern u1="&#x127;" u2="&#x2a;" k="8" />
    <hkern u1="&#x127;" u2="&#x29;" k="11" />
    <hkern u1="&#x128;" g2="parenright.cap" k="-10" />
    <hkern u1="&#x128;" u2="&#xf0;" k="8" />
    <hkern u1="&#x128;" u2="&#xec;" k="-8" />
    <hkern u1="&#x128;" u2="f" k="6" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-13" />
    <hkern u1="&#x129;" u2="&#xef;" k="-8" />
    <hkern u1="&#x129;" u2="&#xec;" k="-18" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-33" />
    <hkern u1="&#x129;" u2="]" k="-34" />
    <hkern u1="&#x129;" u2="\" k="-31" />
    <hkern u1="&#x129;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x129;" u2="&#x29;" k="-9" />
    <hkern u1="&#x129;" u2="&#x27;" k="-11" />
    <hkern u1="&#x129;" u2="&#x22;" k="-11" />
    <hkern u1="&#x12a;" g2="braceright.cap" k="-25" />
    <hkern u1="&#x12a;" g2="parenright.cap" k="-36" />
    <hkern u1="&#x12a;" u2="&#xf0;" k="8" />
    <hkern u1="&#x12a;" u2="&#xec;" k="-8" />
    <hkern u1="&#x12a;" u2="f" k="6" />
    <hkern u1="&#x12b;" u2="&#xef;" k="-8" />
    <hkern u1="&#x12b;" u2="&#xec;" k="-18" />
    <hkern u1="&#x12b;" u2="\" k="-8" />
    <hkern u1="&#x12b;" u2="&#x3f;" k="-11" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-22" />
    <hkern u1="&#x12c;" u2="&#xf0;" k="8" />
    <hkern u1="&#x12c;" u2="&#xec;" k="-8" />
    <hkern u1="&#x12c;" u2="f" k="6" />
    <hkern u1="&#x12d;" u2="&#xef;" k="-8" />
    <hkern u1="&#x12d;" u2="&#xec;" k="-18" />
    <hkern u1="&#x12d;" u2="&#x7d;" k="-24" />
    <hkern u1="&#x12d;" u2="]" k="-25" />
    <hkern u1="&#x12d;" u2="\" k="-8" />
    <hkern u1="&#x12d;" u2="&#x3f;" k="-11" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-13" />
    <hkern u1="&#x12d;" u2="&#x29;" k="-25" />
    <hkern u1="&#x12e;" u2="&#xf0;" k="8" />
    <hkern u1="&#x12e;" u2="&#xec;" k="-8" />
    <hkern u1="&#x12e;" u2="f" k="6" />
    <hkern u1="&#x12f;" u2="&#xef;" k="-8" />
    <hkern u1="&#x12f;" u2="&#xec;" k="-18" />
    <hkern u1="&#x130;" u2="&#xf0;" k="8" />
    <hkern u1="&#x130;" u2="&#xec;" k="-8" />
    <hkern u1="&#x130;" u2="f" k="6" />
    <hkern u1="&#x131;" u2="&#xef;" k="-8" />
    <hkern u1="&#x131;" u2="&#xec;" k="-18" />
    <hkern u1="&#x134;" g2="braceright.cap" k="-42" />
    <hkern u1="&#x134;" g2="bracketright.cap" k="-44" />
    <hkern u1="&#x134;" g2="parenright.cap" k="-46" />
    <hkern u1="&#x134;" u2="&#xf0;" k="7" />
    <hkern u1="&#x134;" u2="&#xec;" k="-11" />
    <hkern u1="&#x134;" u2="f" k="5" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-14" />
    <hkern u1="&#x135;" u2="&#xef;" k="-8" />
    <hkern u1="&#x135;" u2="&#xec;" k="-18" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-33" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-37" />
    <hkern u1="&#x135;" u2="&#x27;" k="-8" />
    <hkern u1="&#x135;" u2="&#x22;" k="-8" />
    <hkern u1="&#x136;" u2="&#x12d;" k="-25" />
    <hkern u1="&#x136;" u2="&#x12b;" k="-17" />
    <hkern u1="&#x136;" u2="&#x129;" k="-35" />
    <hkern u1="&#x136;" u2="&#xf0;" k="13" />
    <hkern u1="&#x136;" u2="&#xef;" k="-51" />
    <hkern u1="&#x136;" u2="&#xee;" k="-6" />
    <hkern u1="&#x136;" u2="&#xec;" k="-47" />
    <hkern u1="&#x136;" u2="v" k="17" />
    <hkern u1="&#x136;" u2="f" k="11" />
    <hkern u1="&#x137;" u2="&#xf0;" k="12" />
    <hkern u1="&#x137;" u2="\" k="17" />
    <hkern u1="&#x137;" u2="V" k="8" />
    <hkern u1="&#x139;" g2="periodcentered.cap" k="64" />
    <hkern u1="&#x139;" u2="&#x2122;" k="76" />
    <hkern u1="&#x139;" u2="&#xae;" k="62" />
    <hkern u1="&#x139;" u2="v" k="32" />
    <hkern u1="&#x139;" u2="f" k="8" />
    <hkern u1="&#x139;" u2="\" k="72" />
    <hkern u1="&#x139;" u2="V" k="55" />
    <hkern u1="&#x139;" u2="&#x2a;" k="75" />
    <hkern u1="&#x13a;" u2="&#xec;" k="-12" />
    <hkern u1="&#x13a;" u2="&#xb7;" k="64" />
    <hkern u1="&#x13b;" g2="periodcentered.cap" k="64" />
    <hkern u1="&#x13b;" u2="&#x2122;" k="76" />
    <hkern u1="&#x13b;" u2="&#xae;" k="62" />
    <hkern u1="&#x13b;" u2="v" k="32" />
    <hkern u1="&#x13b;" u2="f" k="8" />
    <hkern u1="&#x13b;" u2="\" k="72" />
    <hkern u1="&#x13b;" u2="V" k="55" />
    <hkern u1="&#x13b;" u2="&#x2a;" k="75" />
    <hkern u1="&#x13c;" u2="&#xec;" k="-12" />
    <hkern u1="&#x13c;" u2="&#xb7;" k="64" />
    <hkern u1="&#x13d;" g2="periodcentered.cap" k="64" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="58" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="49" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="45" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="49" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="45" />
    <hkern u1="&#x13d;" u2="&#x1ef8;" k="4" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="8" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="27" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="27" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="27" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="21" />
    <hkern u1="&#x13d;" u2="&#x178;" k="8" />
    <hkern u1="&#x13d;" u2="&#x176;" k="8" />
    <hkern u1="&#x13d;" u2="&#x174;" k="27" />
    <hkern u1="&#x13d;" u2="&#x166;" k="21" />
    <hkern u1="&#x13d;" u2="&#x164;" k="21" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="8" />
    <hkern u1="&#x13d;" u2="&#xae;" k="54" />
    <hkern u1="&#x13d;" u2="v" k="32" />
    <hkern u1="&#x13d;" u2="f" k="8" />
    <hkern u1="&#x13d;" u2="\" k="40" />
    <hkern u1="&#x13d;" u2="Y" k="8" />
    <hkern u1="&#x13d;" u2="W" k="27" />
    <hkern u1="&#x13d;" u2="V" k="22" />
    <hkern u1="&#x13d;" u2="T" k="21" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="20" />
    <hkern u1="&#x13d;" u2="&#x27;" k="65" />
    <hkern u1="&#x13d;" u2="&#x22;" k="65" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-20" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-22" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-17" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-13" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-21" />
    <hkern u1="&#x13e;" u2="&#xe1;" k="-9" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-23" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-45" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-13" />
    <hkern u1="&#x13e;" u2="x" k="-23" />
    <hkern u1="&#x13e;" u2="v" k="-22" />
    <hkern u1="&#x13e;" u2="f" k="-5" />
    <hkern u1="&#x13e;" u2="]" k="-46" />
    <hkern u1="&#x13e;" u2="\" k="-46" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-47" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="35" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-48" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-29" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-13" />
    <hkern u1="&#x141;" g2="periodcentered.cap" k="64" />
    <hkern u1="&#x141;" u2="&#x2122;" k="76" />
    <hkern u1="&#x141;" u2="&#xae;" k="62" />
    <hkern u1="&#x141;" u2="v" k="32" />
    <hkern u1="&#x141;" u2="f" k="8" />
    <hkern u1="&#x141;" u2="\" k="72" />
    <hkern u1="&#x141;" u2="V" k="55" />
    <hkern u1="&#x141;" u2="&#x2a;" k="75" />
    <hkern u1="&#x142;" u2="&#xec;" k="-12" />
    <hkern u1="&#x142;" u2="&#xb7;" k="64" />
    <hkern u1="&#x143;" u2="&#xf0;" k="8" />
    <hkern u1="&#x143;" u2="&#xec;" k="-8" />
    <hkern u1="&#x143;" u2="f" k="6" />
    <hkern u1="&#x144;" u2="&#x2122;" k="14" />
    <hkern u1="&#x144;" u2="v" k="4" />
    <hkern u1="&#x144;" u2="\" k="40" />
    <hkern u1="&#x144;" u2="V" k="27" />
    <hkern u1="&#x144;" u2="&#x3f;" k="14" />
    <hkern u1="&#x144;" u2="&#x2a;" k="8" />
    <hkern u1="&#x144;" u2="&#x29;" k="11" />
    <hkern u1="&#x145;" u2="&#xf0;" k="8" />
    <hkern u1="&#x145;" u2="&#xec;" k="-8" />
    <hkern u1="&#x145;" u2="f" k="6" />
    <hkern u1="&#x146;" u2="&#x2122;" k="14" />
    <hkern u1="&#x146;" u2="v" k="4" />
    <hkern u1="&#x146;" u2="\" k="40" />
    <hkern u1="&#x146;" u2="V" k="27" />
    <hkern u1="&#x146;" u2="&#x3f;" k="14" />
    <hkern u1="&#x146;" u2="&#x2a;" k="8" />
    <hkern u1="&#x146;" u2="&#x29;" k="11" />
    <hkern u1="&#x147;" u2="&#xf0;" k="8" />
    <hkern u1="&#x147;" u2="&#xec;" k="-8" />
    <hkern u1="&#x147;" u2="f" k="6" />
    <hkern u1="&#x148;" u2="&#x2122;" k="14" />
    <hkern u1="&#x148;" u2="v" k="4" />
    <hkern u1="&#x148;" u2="\" k="40" />
    <hkern u1="&#x148;" u2="V" k="27" />
    <hkern u1="&#x148;" u2="&#x3f;" k="14" />
    <hkern u1="&#x148;" u2="&#x2a;" k="8" />
    <hkern u1="&#x148;" u2="&#x29;" k="11" />
    <hkern u1="&#x14a;" u2="&#xf0;" k="8" />
    <hkern u1="&#x14a;" u2="&#xec;" k="-8" />
    <hkern u1="&#x14a;" u2="f" k="6" />
    <hkern u1="&#x14b;" u2="&#x2122;" k="14" />
    <hkern u1="&#x14b;" u2="v" k="4" />
    <hkern u1="&#x14b;" u2="\" k="40" />
    <hkern u1="&#x14b;" u2="V" k="27" />
    <hkern u1="&#x14b;" u2="&#x3f;" k="14" />
    <hkern u1="&#x14b;" u2="&#x2a;" k="8" />
    <hkern u1="&#x14b;" u2="&#x29;" k="11" />
    <hkern u1="&#x14c;" g2="braceright.cap" k="13" />
    <hkern u1="&#x14c;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x14c;" g2="parenright.cap" k="14" />
    <hkern u1="&#x14c;" u2="&#xc6;" k="13" />
    <hkern u1="&#x14c;" u2="&#x7d;" k="12" />
    <hkern u1="&#x14c;" u2="]" k="20" />
    <hkern u1="&#x14c;" u2="\" k="19" />
    <hkern u1="&#x14c;" u2="X" k="17" />
    <hkern u1="&#x14c;" u2="V" k="10" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="14" />
    <hkern u1="&#x14c;" u2="&#x29;" k="13" />
    <hkern u1="&#x14d;" u2="&#x2122;" k="14" />
    <hkern u1="&#x14d;" u2="&#xc6;" k="7" />
    <hkern u1="&#x14d;" u2="&#x7d;" k="19" />
    <hkern u1="&#x14d;" u2="x" k="10" />
    <hkern u1="&#x14d;" u2="v" k="7" />
    <hkern u1="&#x14d;" u2="]" k="27" />
    <hkern u1="&#x14d;" u2="\" k="41" />
    <hkern u1="&#x14d;" u2="X" k="26" />
    <hkern u1="&#x14d;" u2="V" k="29" />
    <hkern u1="&#x14d;" u2="&#x3f;" k="15" />
    <hkern u1="&#x14d;" u2="&#x2a;" k="8" />
    <hkern u1="&#x14d;" u2="&#x29;" k="20" />
    <hkern u1="&#x14e;" g2="braceright.cap" k="13" />
    <hkern u1="&#x14e;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x14e;" g2="parenright.cap" k="14" />
    <hkern u1="&#x14e;" u2="&#xc6;" k="13" />
    <hkern u1="&#x14e;" u2="&#x7d;" k="12" />
    <hkern u1="&#x14e;" u2="]" k="20" />
    <hkern u1="&#x14e;" u2="\" k="19" />
    <hkern u1="&#x14e;" u2="X" k="17" />
    <hkern u1="&#x14e;" u2="V" k="10" />
    <hkern u1="&#x14e;" u2="&#x2f;" k="14" />
    <hkern u1="&#x14e;" u2="&#x29;" k="13" />
    <hkern u1="&#x14f;" u2="&#x2122;" k="14" />
    <hkern u1="&#x14f;" u2="&#xc6;" k="7" />
    <hkern u1="&#x14f;" u2="&#x7d;" k="19" />
    <hkern u1="&#x14f;" u2="x" k="10" />
    <hkern u1="&#x14f;" u2="v" k="7" />
    <hkern u1="&#x14f;" u2="]" k="27" />
    <hkern u1="&#x14f;" u2="\" k="41" />
    <hkern u1="&#x14f;" u2="X" k="26" />
    <hkern u1="&#x14f;" u2="V" k="29" />
    <hkern u1="&#x14f;" u2="&#x3f;" k="15" />
    <hkern u1="&#x14f;" u2="&#x2a;" k="8" />
    <hkern u1="&#x14f;" u2="&#x29;" k="20" />
    <hkern u1="&#x150;" g2="braceright.cap" k="13" />
    <hkern u1="&#x150;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x150;" g2="parenright.cap" k="14" />
    <hkern u1="&#x150;" u2="&#xc6;" k="13" />
    <hkern u1="&#x150;" u2="&#x7d;" k="12" />
    <hkern u1="&#x150;" u2="]" k="20" />
    <hkern u1="&#x150;" u2="\" k="19" />
    <hkern u1="&#x150;" u2="X" k="17" />
    <hkern u1="&#x150;" u2="V" k="10" />
    <hkern u1="&#x150;" u2="&#x2f;" k="14" />
    <hkern u1="&#x150;" u2="&#x29;" k="13" />
    <hkern u1="&#x151;" u2="&#x2122;" k="14" />
    <hkern u1="&#x151;" u2="&#xc6;" k="7" />
    <hkern u1="&#x151;" u2="&#x7d;" k="19" />
    <hkern u1="&#x151;" u2="x" k="10" />
    <hkern u1="&#x151;" u2="v" k="7" />
    <hkern u1="&#x151;" u2="]" k="27" />
    <hkern u1="&#x151;" u2="\" k="41" />
    <hkern u1="&#x151;" u2="X" k="26" />
    <hkern u1="&#x151;" u2="V" k="29" />
    <hkern u1="&#x151;" u2="&#x3f;" k="15" />
    <hkern u1="&#x151;" u2="&#x2a;" k="8" />
    <hkern u1="&#x151;" u2="&#x29;" k="20" />
    <hkern u1="&#x152;" u2="&#x135;" k="-21" />
    <hkern u1="&#x152;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x152;" u2="&#x129;" k="-22" />
    <hkern u1="&#x152;" u2="&#xf0;" k="6" />
    <hkern u1="&#x152;" u2="&#xef;" k="-32" />
    <hkern u1="&#x152;" u2="&#xee;" k="-29" />
    <hkern u1="&#x152;" u2="&#xec;" k="-42" />
    <hkern u1="&#x153;" u2="&#x2122;" k="12" />
    <hkern u1="&#x153;" u2="&#xc6;" k="5" />
    <hkern u1="&#x153;" u2="&#x7d;" k="10" />
    <hkern u1="&#x153;" u2="v" k="6" />
    <hkern u1="&#x153;" u2="\" k="38" />
    <hkern u1="&#x153;" u2="X" k="5" />
    <hkern u1="&#x153;" u2="V" k="25" />
    <hkern u1="&#x153;" u2="&#x3f;" k="11" />
    <hkern u1="&#x153;" u2="&#x29;" k="12" />
    <hkern u1="&#x154;" u2="&#xf0;" k="14" />
    <hkern u1="&#x154;" u2="&#xc6;" k="10" />
    <hkern u1="&#x154;" u2="\" k="15" />
    <hkern u1="&#x154;" u2="X" k="6" />
    <hkern u1="&#x154;" u2="V" k="9" />
    <hkern u1="&#x155;" u2="&#xf0;" k="19" />
    <hkern u1="&#x155;" u2="&#xc6;" k="31" />
    <hkern u1="&#x155;" u2="&#x7d;" k="12" />
    <hkern u1="&#x155;" u2="]" k="19" />
    <hkern u1="&#x155;" u2="\" k="11" />
    <hkern u1="&#x155;" u2="X" k="29" />
    <hkern u1="&#x155;" u2="&#x2f;" k="33" />
    <hkern u1="&#x155;" u2="&#x29;" k="10" />
    <hkern u1="&#x156;" u2="&#xf0;" k="14" />
    <hkern u1="&#x156;" u2="&#xc6;" k="10" />
    <hkern u1="&#x156;" u2="\" k="15" />
    <hkern u1="&#x156;" u2="X" k="6" />
    <hkern u1="&#x156;" u2="V" k="9" />
    <hkern u1="&#x157;" u2="&#xf0;" k="19" />
    <hkern u1="&#x157;" u2="&#xc6;" k="31" />
    <hkern u1="&#x157;" u2="&#x7d;" k="12" />
    <hkern u1="&#x157;" u2="]" k="19" />
    <hkern u1="&#x157;" u2="\" k="11" />
    <hkern u1="&#x157;" u2="X" k="29" />
    <hkern u1="&#x157;" u2="&#x2f;" k="33" />
    <hkern u1="&#x157;" u2="&#x29;" k="10" />
    <hkern u1="&#x158;" u2="&#xf0;" k="14" />
    <hkern u1="&#x158;" u2="&#xc6;" k="10" />
    <hkern u1="&#x158;" u2="\" k="15" />
    <hkern u1="&#x158;" u2="X" k="6" />
    <hkern u1="&#x158;" u2="V" k="9" />
    <hkern u1="&#x159;" u2="&#xf0;" k="19" />
    <hkern u1="&#x159;" u2="&#xc6;" k="31" />
    <hkern u1="&#x159;" u2="&#x7d;" k="12" />
    <hkern u1="&#x159;" u2="]" k="19" />
    <hkern u1="&#x159;" u2="\" k="11" />
    <hkern u1="&#x159;" u2="X" k="29" />
    <hkern u1="&#x159;" u2="&#x2f;" k="33" />
    <hkern u1="&#x159;" u2="&#x29;" k="10" />
    <hkern u1="&#x15a;" u2="&#x129;" k="-10" />
    <hkern u1="&#x15a;" u2="&#xef;" k="-20" />
    <hkern u1="&#x15a;" u2="&#xee;" k="-8" />
    <hkern u1="&#x15a;" u2="&#xec;" k="-28" />
    <hkern u1="&#x15a;" u2="&#xc6;" k="10" />
    <hkern u1="&#x15a;" u2="x" k="8" />
    <hkern u1="&#x15a;" u2="v" k="6" />
    <hkern u1="&#x15a;" u2="f" k="9" />
    <hkern u1="&#x15a;" u2="X" k="5" />
    <hkern u1="&#x15a;" u2="V" k="8" />
    <hkern u1="&#x15b;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15b;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15b;" u2="&#x7d;" k="12" />
    <hkern u1="&#x15b;" u2="v" k="5" />
    <hkern u1="&#x15b;" u2="]" k="18" />
    <hkern u1="&#x15b;" u2="\" k="29" />
    <hkern u1="&#x15b;" u2="X" k="8" />
    <hkern u1="&#x15b;" u2="V" k="18" />
    <hkern u1="&#x15b;" u2="&#x29;" k="13" />
    <hkern u1="&#x15c;" u2="&#x129;" k="-10" />
    <hkern u1="&#x15c;" u2="&#xef;" k="-20" />
    <hkern u1="&#x15c;" u2="&#xee;" k="-8" />
    <hkern u1="&#x15c;" u2="&#xec;" k="-28" />
    <hkern u1="&#x15c;" u2="&#xc6;" k="10" />
    <hkern u1="&#x15c;" u2="x" k="8" />
    <hkern u1="&#x15c;" u2="v" k="6" />
    <hkern u1="&#x15c;" u2="f" k="9" />
    <hkern u1="&#x15c;" u2="X" k="5" />
    <hkern u1="&#x15c;" u2="V" k="8" />
    <hkern u1="&#x15d;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15d;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15d;" u2="&#x7d;" k="12" />
    <hkern u1="&#x15d;" u2="v" k="5" />
    <hkern u1="&#x15d;" u2="]" k="18" />
    <hkern u1="&#x15d;" u2="\" k="29" />
    <hkern u1="&#x15d;" u2="X" k="8" />
    <hkern u1="&#x15d;" u2="V" k="18" />
    <hkern u1="&#x15d;" u2="&#x29;" k="13" />
    <hkern u1="&#x15e;" u2="&#x129;" k="-10" />
    <hkern u1="&#x15e;" u2="&#xef;" k="-20" />
    <hkern u1="&#x15e;" u2="&#xee;" k="-8" />
    <hkern u1="&#x15e;" u2="&#xec;" k="-28" />
    <hkern u1="&#x15e;" u2="&#xc6;" k="10" />
    <hkern u1="&#x15e;" u2="x" k="8" />
    <hkern u1="&#x15e;" u2="v" k="6" />
    <hkern u1="&#x15e;" u2="f" k="9" />
    <hkern u1="&#x15e;" u2="X" k="5" />
    <hkern u1="&#x15e;" u2="V" k="8" />
    <hkern u1="&#x15f;" u2="&#x2122;" k="11" />
    <hkern u1="&#x15f;" u2="&#xc6;" k="5" />
    <hkern u1="&#x15f;" u2="&#x7d;" k="12" />
    <hkern u1="&#x15f;" u2="v" k="5" />
    <hkern u1="&#x15f;" u2="]" k="18" />
    <hkern u1="&#x15f;" u2="\" k="29" />
    <hkern u1="&#x15f;" u2="X" k="8" />
    <hkern u1="&#x15f;" u2="V" k="18" />
    <hkern u1="&#x15f;" u2="&#x29;" k="13" />
    <hkern u1="&#x160;" u2="&#x129;" k="-10" />
    <hkern u1="&#x160;" u2="&#xef;" k="-20" />
    <hkern u1="&#x160;" u2="&#xee;" k="-8" />
    <hkern u1="&#x160;" u2="&#xec;" k="-28" />
    <hkern u1="&#x160;" u2="&#xc6;" k="10" />
    <hkern u1="&#x160;" u2="x" k="8" />
    <hkern u1="&#x160;" u2="v" k="6" />
    <hkern u1="&#x160;" u2="f" k="9" />
    <hkern u1="&#x160;" u2="X" k="5" />
    <hkern u1="&#x160;" u2="V" k="8" />
    <hkern u1="&#x161;" u2="&#x2122;" k="11" />
    <hkern u1="&#x161;" u2="&#xc6;" k="5" />
    <hkern u1="&#x161;" u2="&#x7d;" k="12" />
    <hkern u1="&#x161;" u2="v" k="5" />
    <hkern u1="&#x161;" u2="]" k="18" />
    <hkern u1="&#x161;" u2="\" k="29" />
    <hkern u1="&#x161;" u2="X" k="8" />
    <hkern u1="&#x161;" u2="V" k="18" />
    <hkern u1="&#x161;" u2="&#x29;" k="13" />
    <hkern u1="&#x164;" u2="&#x1ef9;" k="50" />
    <hkern u1="&#x164;" u2="&#x1eab;" k="50" />
    <hkern u1="&#x164;" u2="&#x16d;" k="54" />
    <hkern u1="&#x164;" u2="&#x169;" k="54" />
    <hkern u1="&#x164;" u2="&#x15d;" k="57" />
    <hkern u1="&#x164;" u2="&#x159;" k="37" />
    <hkern u1="&#x164;" u2="&#x155;" k="38" />
    <hkern u1="&#x164;" u2="&#x151;" k="42" />
    <hkern u1="&#x164;" u2="&#x135;" k="-48" />
    <hkern u1="&#x164;" u2="&#x131;" k="52" />
    <hkern u1="&#x164;" u2="&#x12d;" k="-32" />
    <hkern u1="&#x164;" u2="&#x12b;" k="-24" />
    <hkern u1="&#x164;" u2="&#x129;" k="-48" />
    <hkern u1="&#x164;" u2="&#x11f;" k="75" />
    <hkern u1="&#x164;" u2="&#x109;" k="43" />
    <hkern u1="&#x164;" u2="&#xf0;" k="18" />
    <hkern u1="&#x164;" u2="&#xef;" k="-58" />
    <hkern u1="&#x164;" u2="&#xee;" k="-55" />
    <hkern u1="&#x164;" u2="&#xec;" k="-68" />
    <hkern u1="&#x164;" u2="&#xe4;" k="54" />
    <hkern u1="&#x164;" u2="&#xe3;" k="44" />
    <hkern u1="&#x164;" u2="&#xc6;" k="49" />
    <hkern u1="&#x164;" u2="x" k="49" />
    <hkern u1="&#x164;" u2="v" k="49" />
    <hkern u1="&#x164;" u2="f" k="15" />
    <hkern u1="&#x164;" u2="&#x40;" k="15" />
    <hkern u1="&#x164;" u2="&#x2f;" k="49" />
    <hkern u1="&#x164;" u2="&#x26;" k="10" />
    <hkern u1="&#x165;" u2="&#x2039;" k="45" />
    <hkern u1="&#x165;" u2="&#x2026;" k="52" />
    <hkern u1="&#x165;" u2="&#x201e;" k="52" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-8" />
    <hkern u1="&#x165;" u2="&#x201a;" k="52" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-8" />
    <hkern u1="&#x165;" u2="&#x2014;" k="58" />
    <hkern u1="&#x165;" u2="&#x2013;" k="58" />
    <hkern u1="&#x165;" u2="&#x1ef9;" k="-26" />
    <hkern u1="&#x165;" u2="&#x1ef3;" k="-26" />
    <hkern u1="&#x165;" u2="&#x1ed7;" k="10" />
    <hkern u1="&#x165;" u2="&#x1ec5;" k="10" />
    <hkern u1="&#x165;" u2="&#x1e85;" k="-18" />
    <hkern u1="&#x165;" u2="&#x1e83;" k="-18" />
    <hkern u1="&#x165;" u2="&#x1e81;" k="-18" />
    <hkern u1="&#x165;" u2="&#x21b;" k="-19" />
    <hkern u1="&#x165;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x165;" u2="&#x177;" k="-26" />
    <hkern u1="&#x165;" u2="&#x175;" k="-18" />
    <hkern u1="&#x165;" u2="&#x167;" k="-19" />
    <hkern u1="&#x165;" u2="&#x165;" k="-19" />
    <hkern u1="&#x165;" u2="&#x153;" k="10" />
    <hkern u1="&#x165;" u2="&#x151;" k="10" />
    <hkern u1="&#x165;" u2="&#x14f;" k="10" />
    <hkern u1="&#x165;" u2="&#x14d;" k="10" />
    <hkern u1="&#x165;" u2="&#x142;" k="-8" />
    <hkern u1="&#x165;" u2="&#x13e;" k="-8" />
    <hkern u1="&#x165;" u2="&#x13c;" k="-8" />
    <hkern u1="&#x165;" u2="&#x13a;" k="-8" />
    <hkern u1="&#x165;" u2="&#x137;" k="-14" />
    <hkern u1="&#x165;" u2="&#x135;" k="-14" />
    <hkern u1="&#x165;" u2="&#x131;" k="-14" />
    <hkern u1="&#x165;" u2="&#x12f;" k="-14" />
    <hkern u1="&#x165;" u2="&#x12d;" k="-14" />
    <hkern u1="&#x165;" u2="&#x12b;" k="-14" />
    <hkern u1="&#x165;" u2="&#x129;" k="-14" />
    <hkern u1="&#x165;" u2="&#x127;" k="-14" />
    <hkern u1="&#x165;" u2="&#x125;" k="-14" />
    <hkern u1="&#x165;" u2="&#x123;" k="5" />
    <hkern u1="&#x165;" u2="&#x121;" k="5" />
    <hkern u1="&#x165;" u2="&#x11f;" k="5" />
    <hkern u1="&#x165;" u2="&#x11d;" k="5" />
    <hkern u1="&#x165;" u2="&#x11b;" k="10" />
    <hkern u1="&#x165;" u2="&#x119;" k="10" />
    <hkern u1="&#x165;" u2="&#x117;" k="10" />
    <hkern u1="&#x165;" u2="&#x115;" k="10" />
    <hkern u1="&#x165;" u2="&#x113;" k="10" />
    <hkern u1="&#x165;" u2="&#x111;" k="8" />
    <hkern u1="&#x165;" u2="&#x10f;" k="8" />
    <hkern u1="&#x165;" u2="&#x10d;" k="10" />
    <hkern u1="&#x165;" u2="&#x10b;" k="10" />
    <hkern u1="&#x165;" u2="&#x109;" k="10" />
    <hkern u1="&#x165;" u2="&#x107;" k="10" />
    <hkern u1="&#x165;" u2="&#xff;" k="-26" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-14" />
    <hkern u1="&#x165;" u2="&#xfd;" k="-26" />
    <hkern u1="&#x165;" u2="&#xf8;" k="10" />
    <hkern u1="&#x165;" u2="&#xf6;" k="10" />
    <hkern u1="&#x165;" u2="&#xf5;" k="10" />
    <hkern u1="&#x165;" u2="&#xf4;" k="10" />
    <hkern u1="&#x165;" u2="&#xf3;" k="10" />
    <hkern u1="&#x165;" u2="&#xf2;" k="10" />
    <hkern u1="&#x165;" u2="&#xf0;" k="6" />
    <hkern u1="&#x165;" u2="&#xef;" k="-14" />
    <hkern u1="&#x165;" u2="&#xee;" k="-14" />
    <hkern u1="&#x165;" u2="&#xed;" k="-14" />
    <hkern u1="&#x165;" u2="&#xec;" k="-14" />
    <hkern u1="&#x165;" u2="&#xeb;" k="10" />
    <hkern u1="&#x165;" u2="&#xea;" k="10" />
    <hkern u1="&#x165;" u2="&#xe9;" k="10" />
    <hkern u1="&#x165;" u2="&#xe8;" k="10" />
    <hkern u1="&#x165;" u2="&#xe7;" k="10" />
    <hkern u1="&#x165;" u2="&#xe4;" k="-11" />
    <hkern u1="&#x165;" u2="&#xdf;" k="-14" />
    <hkern u1="&#x165;" u2="&#xab;" k="45" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x165;" u2="y" k="-26" />
    <hkern u1="&#x165;" u2="x" k="-27" />
    <hkern u1="&#x165;" u2="w" k="-18" />
    <hkern u1="&#x165;" u2="v" k="-27" />
    <hkern u1="&#x165;" u2="t" k="-19" />
    <hkern u1="&#x165;" u2="q" k="8" />
    <hkern u1="&#x165;" u2="o" k="10" />
    <hkern u1="&#x165;" u2="l" k="-8" />
    <hkern u1="&#x165;" u2="k" k="-14" />
    <hkern u1="&#x165;" u2="j" k="-14" />
    <hkern u1="&#x165;" u2="i" k="-14" />
    <hkern u1="&#x165;" u2="h" k="-14" />
    <hkern u1="&#x165;" u2="g" k="5" />
    <hkern u1="&#x165;" u2="f" k="-9" />
    <hkern u1="&#x165;" u2="e" k="10" />
    <hkern u1="&#x165;" u2="d" k="8" />
    <hkern u1="&#x165;" u2="c" k="10" />
    <hkern u1="&#x165;" u2="b" k="-14" />
    <hkern u1="&#x165;" u2="]" k="-9" />
    <hkern u1="&#x165;" u2="\" k="-13" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-14" />
    <hkern u1="&#x165;" u2="&#x2f;" k="36" />
    <hkern u1="&#x165;" u2="&#x2e;" k="52" />
    <hkern u1="&#x165;" u2="&#x2d;" k="58" />
    <hkern u1="&#x165;" u2="&#x2c;" k="52" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-29" />
    <hkern u1="&#x166;" u2="&#x1ef9;" k="50" />
    <hkern u1="&#x166;" u2="&#x1eab;" k="50" />
    <hkern u1="&#x166;" u2="&#x16d;" k="54" />
    <hkern u1="&#x166;" u2="&#x169;" k="54" />
    <hkern u1="&#x166;" u2="&#x15d;" k="57" />
    <hkern u1="&#x166;" u2="&#x159;" k="37" />
    <hkern u1="&#x166;" u2="&#x155;" k="38" />
    <hkern u1="&#x166;" u2="&#x151;" k="42" />
    <hkern u1="&#x166;" u2="&#x135;" k="-48" />
    <hkern u1="&#x166;" u2="&#x131;" k="52" />
    <hkern u1="&#x166;" u2="&#x12d;" k="-32" />
    <hkern u1="&#x166;" u2="&#x12b;" k="-24" />
    <hkern u1="&#x166;" u2="&#x129;" k="-48" />
    <hkern u1="&#x166;" u2="&#x11f;" k="75" />
    <hkern u1="&#x166;" u2="&#x109;" k="43" />
    <hkern u1="&#x166;" u2="&#xf0;" k="18" />
    <hkern u1="&#x166;" u2="&#xef;" k="-58" />
    <hkern u1="&#x166;" u2="&#xee;" k="-55" />
    <hkern u1="&#x166;" u2="&#xec;" k="-68" />
    <hkern u1="&#x166;" u2="&#xe4;" k="54" />
    <hkern u1="&#x166;" u2="&#xe3;" k="44" />
    <hkern u1="&#x166;" u2="&#xc6;" k="49" />
    <hkern u1="&#x166;" u2="x" k="49" />
    <hkern u1="&#x166;" u2="v" k="49" />
    <hkern u1="&#x166;" u2="f" k="15" />
    <hkern u1="&#x166;" u2="&#x40;" k="15" />
    <hkern u1="&#x166;" u2="&#x2f;" k="49" />
    <hkern u1="&#x166;" u2="&#x26;" k="10" />
    <hkern u1="&#x167;" u2="\" k="17" />
    <hkern u1="&#x167;" u2="V" k="6" />
    <hkern u1="&#x168;" u2="&#xf0;" k="8" />
    <hkern u1="&#x168;" u2="&#xec;" k="-14" />
    <hkern u1="&#x168;" u2="&#xc6;" k="7" />
    <hkern u1="&#x168;" u2="f" k="5" />
    <hkern u1="&#x168;" u2="&#x2f;" k="16" />
    <hkern u1="&#x169;" u2="&#x2122;" k="9" />
    <hkern u1="&#x169;" u2="\" k="28" />
    <hkern u1="&#x169;" u2="X" k="5" />
    <hkern u1="&#x169;" u2="V" k="21" />
    <hkern u1="&#x169;" u2="&#x29;" k="11" />
    <hkern u1="&#x16a;" u2="&#xf0;" k="8" />
    <hkern u1="&#x16a;" u2="&#xec;" k="-14" />
    <hkern u1="&#x16a;" u2="&#xc6;" k="7" />
    <hkern u1="&#x16a;" u2="f" k="5" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="16" />
    <hkern u1="&#x16b;" u2="&#x2122;" k="9" />
    <hkern u1="&#x16b;" u2="\" k="28" />
    <hkern u1="&#x16b;" u2="X" k="5" />
    <hkern u1="&#x16b;" u2="V" k="21" />
    <hkern u1="&#x16b;" u2="&#x29;" k="11" />
    <hkern u1="&#x16c;" u2="&#xf0;" k="8" />
    <hkern u1="&#x16c;" u2="&#xec;" k="-14" />
    <hkern u1="&#x16c;" u2="&#xc6;" k="7" />
    <hkern u1="&#x16c;" u2="f" k="5" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="16" />
    <hkern u1="&#x16d;" u2="&#x2122;" k="9" />
    <hkern u1="&#x16d;" u2="\" k="28" />
    <hkern u1="&#x16d;" u2="X" k="5" />
    <hkern u1="&#x16d;" u2="V" k="21" />
    <hkern u1="&#x16d;" u2="&#x29;" k="11" />
    <hkern u1="&#x16e;" u2="&#xf0;" k="8" />
    <hkern u1="&#x16e;" u2="&#xec;" k="-14" />
    <hkern u1="&#x16e;" u2="&#xc6;" k="7" />
    <hkern u1="&#x16e;" u2="f" k="5" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="16" />
    <hkern u1="&#x16f;" u2="&#x2122;" k="9" />
    <hkern u1="&#x16f;" u2="\" k="28" />
    <hkern u1="&#x16f;" u2="X" k="5" />
    <hkern u1="&#x16f;" u2="V" k="21" />
    <hkern u1="&#x16f;" u2="&#x29;" k="11" />
    <hkern u1="&#x170;" u2="&#xf0;" k="8" />
    <hkern u1="&#x170;" u2="&#xec;" k="-14" />
    <hkern u1="&#x170;" u2="&#xc6;" k="7" />
    <hkern u1="&#x170;" u2="f" k="5" />
    <hkern u1="&#x170;" u2="&#x2f;" k="16" />
    <hkern u1="&#x171;" u2="&#x2122;" k="9" />
    <hkern u1="&#x171;" u2="\" k="28" />
    <hkern u1="&#x171;" u2="X" k="5" />
    <hkern u1="&#x171;" u2="V" k="21" />
    <hkern u1="&#x171;" u2="&#x29;" k="11" />
    <hkern u1="&#x172;" u2="&#xf0;" k="8" />
    <hkern u1="&#x172;" u2="&#xec;" k="-14" />
    <hkern u1="&#x172;" u2="&#xc6;" k="7" />
    <hkern u1="&#x172;" u2="f" k="5" />
    <hkern u1="&#x172;" u2="&#x2f;" k="16" />
    <hkern u1="&#x173;" u2="&#x2122;" k="9" />
    <hkern u1="&#x173;" u2="\" k="28" />
    <hkern u1="&#x173;" u2="X" k="5" />
    <hkern u1="&#x173;" u2="V" k="21" />
    <hkern u1="&#x173;" u2="&#x29;" k="11" />
    <hkern u1="&#x174;" u2="&#x135;" k="-24" />
    <hkern u1="&#x174;" u2="&#x131;" k="13" />
    <hkern u1="&#x174;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x174;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x174;" u2="&#x129;" k="-39" />
    <hkern u1="&#x174;" u2="&#xf0;" k="13" />
    <hkern u1="&#x174;" u2="&#xef;" k="-53" />
    <hkern u1="&#x174;" u2="&#xee;" k="-30" />
    <hkern u1="&#x174;" u2="&#xec;" k="-54" />
    <hkern u1="&#x174;" u2="&#xc6;" k="23" />
    <hkern u1="&#x174;" u2="&#x2f;" k="29" />
    <hkern u1="&#x175;" u2="&#xf0;" k="7" />
    <hkern u1="&#x175;" u2="&#xc6;" k="14" />
    <hkern u1="&#x175;" u2="&#x7d;" k="15" />
    <hkern u1="&#x175;" u2="]" k="21" />
    <hkern u1="&#x175;" u2="\" k="18" />
    <hkern u1="&#x175;" u2="X" k="23" />
    <hkern u1="&#x175;" u2="V" k="9" />
    <hkern u1="&#x175;" u2="&#x2f;" k="14" />
    <hkern u1="&#x175;" u2="&#x29;" k="14" />
    <hkern u1="&#x176;" u2="&#x1ef9;" k="24" />
    <hkern u1="&#x176;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#x176;" u2="&#x159;" k="26" />
    <hkern u1="&#x176;" u2="&#x155;" k="32" />
    <hkern u1="&#x176;" u2="&#x151;" k="48" />
    <hkern u1="&#x176;" u2="&#x142;" k="6" />
    <hkern u1="&#x176;" u2="&#x135;" k="-15" />
    <hkern u1="&#x176;" u2="&#x131;" k="59" />
    <hkern u1="&#x176;" u2="&#x12d;" k="-44" />
    <hkern u1="&#x176;" u2="&#x12b;" k="-36" />
    <hkern u1="&#x176;" u2="&#x129;" k="-49" />
    <hkern u1="&#x176;" u2="&#x103;" k="56" />
    <hkern u1="&#x176;" u2="&#xff;" k="24" />
    <hkern u1="&#x176;" u2="&#xf0;" k="26" />
    <hkern u1="&#x176;" u2="&#xef;" k="-70" />
    <hkern u1="&#x176;" u2="&#xee;" k="-21" />
    <hkern u1="&#x176;" u2="&#xec;" k="-62" />
    <hkern u1="&#x176;" u2="&#xeb;" k="61" />
    <hkern u1="&#x176;" u2="&#xe4;" k="43" />
    <hkern u1="&#x176;" u2="&#xe3;" k="34" />
    <hkern u1="&#x176;" u2="&#xdf;" k="14" />
    <hkern u1="&#x176;" u2="&#xc6;" k="59" />
    <hkern u1="&#x176;" u2="&#xae;" k="19" />
    <hkern u1="&#x176;" u2="x" k="34" />
    <hkern u1="&#x176;" u2="v" k="33" />
    <hkern u1="&#x176;" u2="f" k="23" />
    <hkern u1="&#x176;" u2="&#x40;" k="35" />
    <hkern u1="&#x176;" u2="&#x2f;" k="65" />
    <hkern u1="&#x176;" u2="&#x2a;" k="-5" />
    <hkern u1="&#x176;" u2="&#x26;" k="28" />
    <hkern u1="&#x177;" u2="&#xf0;" k="13" />
    <hkern u1="&#x177;" u2="&#xc6;" k="18" />
    <hkern u1="&#x177;" u2="&#x7d;" k="11" />
    <hkern u1="&#x177;" u2="]" k="19" />
    <hkern u1="&#x177;" u2="\" k="18" />
    <hkern u1="&#x177;" u2="X" k="22" />
    <hkern u1="&#x177;" u2="V" k="6" />
    <hkern u1="&#x177;" u2="&#x2f;" k="19" />
    <hkern u1="&#x178;" u2="&#x1ef9;" k="24" />
    <hkern u1="&#x178;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#x178;" u2="&#x159;" k="26" />
    <hkern u1="&#x178;" u2="&#x155;" k="32" />
    <hkern u1="&#x178;" u2="&#x151;" k="48" />
    <hkern u1="&#x178;" u2="&#x142;" k="6" />
    <hkern u1="&#x178;" u2="&#x135;" k="-15" />
    <hkern u1="&#x178;" u2="&#x131;" k="59" />
    <hkern u1="&#x178;" u2="&#x12d;" k="-44" />
    <hkern u1="&#x178;" u2="&#x12b;" k="-36" />
    <hkern u1="&#x178;" u2="&#x129;" k="-49" />
    <hkern u1="&#x178;" u2="&#x103;" k="56" />
    <hkern u1="&#x178;" u2="&#xff;" k="24" />
    <hkern u1="&#x178;" u2="&#xf0;" k="26" />
    <hkern u1="&#x178;" u2="&#xef;" k="-70" />
    <hkern u1="&#x178;" u2="&#xee;" k="-21" />
    <hkern u1="&#x178;" u2="&#xec;" k="-62" />
    <hkern u1="&#x178;" u2="&#xeb;" k="61" />
    <hkern u1="&#x178;" u2="&#xe4;" k="43" />
    <hkern u1="&#x178;" u2="&#xe3;" k="34" />
    <hkern u1="&#x178;" u2="&#xdf;" k="14" />
    <hkern u1="&#x178;" u2="&#xc6;" k="59" />
    <hkern u1="&#x178;" u2="&#xae;" k="19" />
    <hkern u1="&#x178;" u2="x" k="34" />
    <hkern u1="&#x178;" u2="v" k="33" />
    <hkern u1="&#x178;" u2="f" k="23" />
    <hkern u1="&#x178;" u2="&#x40;" k="35" />
    <hkern u1="&#x178;" u2="&#x2f;" k="65" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-5" />
    <hkern u1="&#x178;" u2="&#x26;" k="28" />
    <hkern u1="&#x179;" u2="&#x135;" k="-20" />
    <hkern u1="&#x179;" u2="&#x129;" k="-20" />
    <hkern u1="&#x179;" u2="&#xf0;" k="7" />
    <hkern u1="&#x179;" u2="&#xef;" k="-30" />
    <hkern u1="&#x179;" u2="&#xee;" k="-28" />
    <hkern u1="&#x179;" u2="&#xec;" k="-41" />
    <hkern u1="&#x179;" u2="v" k="7" />
    <hkern u1="&#x179;" u2="f" k="7" />
    <hkern u1="&#x17a;" u2="&#xf0;" k="4" />
    <hkern u1="&#x17a;" u2="\" k="25" />
    <hkern u1="&#x17a;" u2="V" k="13" />
    <hkern u1="&#x17b;" u2="&#x135;" k="-20" />
    <hkern u1="&#x17b;" u2="&#x129;" k="-20" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="7" />
    <hkern u1="&#x17b;" u2="&#xef;" k="-30" />
    <hkern u1="&#x17b;" u2="&#xee;" k="-28" />
    <hkern u1="&#x17b;" u2="&#xec;" k="-41" />
    <hkern u1="&#x17b;" u2="v" k="7" />
    <hkern u1="&#x17b;" u2="f" k="7" />
    <hkern u1="&#x17c;" u2="&#xf0;" k="4" />
    <hkern u1="&#x17c;" u2="\" k="25" />
    <hkern u1="&#x17c;" u2="V" k="13" />
    <hkern u1="&#x17d;" u2="&#x135;" k="-20" />
    <hkern u1="&#x17d;" u2="&#x129;" k="-20" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="7" />
    <hkern u1="&#x17d;" u2="&#xef;" k="-30" />
    <hkern u1="&#x17d;" u2="&#xee;" k="-28" />
    <hkern u1="&#x17d;" u2="&#xec;" k="-41" />
    <hkern u1="&#x17d;" u2="v" k="7" />
    <hkern u1="&#x17d;" u2="f" k="7" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="4" />
    <hkern u1="&#x17e;" u2="\" k="25" />
    <hkern u1="&#x17e;" u2="V" k="13" />
    <hkern u1="&#x1fa;" u2="&#x2122;" k="30" />
    <hkern u1="&#x1fa;" u2="&#xf0;" k="5" />
    <hkern u1="&#x1fa;" u2="&#xae;" k="18" />
    <hkern u1="&#x1fa;" u2="v" k="14" />
    <hkern u1="&#x1fa;" u2="f" k="8" />
    <hkern u1="&#x1fa;" u2="\" k="42" />
    <hkern u1="&#x1fa;" u2="V" k="27" />
    <hkern u1="&#x1fa;" u2="&#x3f;" k="14" />
    <hkern u1="&#x1fa;" u2="&#x2a;" k="27" />
    <hkern u1="&#x1fb;" u2="&#x2122;" k="15" />
    <hkern u1="&#x1fb;" u2="v" k="6" />
    <hkern u1="&#x1fb;" u2="\" k="43" />
    <hkern u1="&#x1fb;" u2="V" k="28" />
    <hkern u1="&#x1fb;" u2="&#x3f;" k="13" />
    <hkern u1="&#x1fb;" u2="&#x2a;" k="8" />
    <hkern u1="&#x1fc;" u2="&#x135;" k="-21" />
    <hkern u1="&#x1fc;" u2="&#x12d;" k="-6" />
    <hkern u1="&#x1fc;" u2="&#x129;" k="-22" />
    <hkern u1="&#x1fc;" u2="&#xf0;" k="6" />
    <hkern u1="&#x1fc;" u2="&#xef;" k="-32" />
    <hkern u1="&#x1fc;" u2="&#xee;" k="-29" />
    <hkern u1="&#x1fc;" u2="&#xec;" k="-42" />
    <hkern u1="&#x1fd;" u2="&#x2122;" k="12" />
    <hkern u1="&#x1fd;" u2="&#xc6;" k="5" />
    <hkern u1="&#x1fd;" u2="&#x7d;" k="10" />
    <hkern u1="&#x1fd;" u2="v" k="6" />
    <hkern u1="&#x1fd;" u2="\" k="38" />
    <hkern u1="&#x1fd;" u2="X" k="5" />
    <hkern u1="&#x1fd;" u2="V" k="25" />
    <hkern u1="&#x1fd;" u2="&#x3f;" k="11" />
    <hkern u1="&#x1fd;" u2="&#x29;" k="12" />
    <hkern u1="&#x1fe;" g2="braceright.cap" k="13" />
    <hkern u1="&#x1fe;" g2="bracketright.cap" k="20" />
    <hkern u1="&#x1fe;" g2="parenright.cap" k="14" />
    <hkern u1="&#x1fe;" u2="&#xc6;" k="13" />
    <hkern u1="&#x1fe;" u2="&#x7d;" k="12" />
    <hkern u1="&#x1fe;" u2="]" k="20" />
    <hkern u1="&#x1fe;" u2="\" k="19" />
    <hkern u1="&#x1fe;" u2="X" k="17" />
    <hkern u1="&#x1fe;" u2="V" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2f;" k="14" />
    <hkern u1="&#x1fe;" u2="&#x29;" k="13" />
    <hkern u1="&#x1ff;" u2="&#x2122;" k="14" />
    <hkern u1="&#x1ff;" u2="&#xc6;" k="7" />
    <hkern u1="&#x1ff;" u2="&#x7d;" k="19" />
    <hkern u1="&#x1ff;" u2="x" k="10" />
    <hkern u1="&#x1ff;" u2="v" k="7" />
    <hkern u1="&#x1ff;" u2="]" k="27" />
    <hkern u1="&#x1ff;" u2="\" k="41" />
    <hkern u1="&#x1ff;" u2="X" k="26" />
    <hkern u1="&#x1ff;" u2="V" k="29" />
    <hkern u1="&#x1ff;" u2="&#x3f;" k="15" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="8" />
    <hkern u1="&#x1ff;" u2="&#x29;" k="20" />
    <hkern u1="&#x218;" u2="&#x129;" k="-10" />
    <hkern u1="&#x218;" u2="&#xef;" k="-20" />
    <hkern u1="&#x218;" u2="&#xee;" k="-8" />
    <hkern u1="&#x218;" u2="&#xec;" k="-28" />
    <hkern u1="&#x218;" u2="&#xc6;" k="10" />
    <hkern u1="&#x218;" u2="x" k="8" />
    <hkern u1="&#x218;" u2="v" k="6" />
    <hkern u1="&#x218;" u2="f" k="9" />
    <hkern u1="&#x218;" u2="X" k="5" />
    <hkern u1="&#x218;" u2="V" k="8" />
    <hkern u1="&#x219;" u2="&#x2122;" k="11" />
    <hkern u1="&#x219;" u2="&#xc6;" k="5" />
    <hkern u1="&#x219;" u2="&#x7d;" k="12" />
    <hkern u1="&#x219;" u2="v" k="5" />
    <hkern u1="&#x219;" u2="]" k="18" />
    <hkern u1="&#x219;" u2="\" k="29" />
    <hkern u1="&#x219;" u2="X" k="8" />
    <hkern u1="&#x219;" u2="V" k="18" />
    <hkern u1="&#x219;" u2="&#x29;" k="13" />
    <hkern u1="&#x21a;" u2="&#x1ef9;" k="50" />
    <hkern u1="&#x21a;" u2="&#x1eab;" k="50" />
    <hkern u1="&#x21a;" u2="&#x16d;" k="54" />
    <hkern u1="&#x21a;" u2="&#x169;" k="54" />
    <hkern u1="&#x21a;" u2="&#x15d;" k="57" />
    <hkern u1="&#x21a;" u2="&#x159;" k="37" />
    <hkern u1="&#x21a;" u2="&#x155;" k="38" />
    <hkern u1="&#x21a;" u2="&#x151;" k="42" />
    <hkern u1="&#x21a;" u2="&#x135;" k="-48" />
    <hkern u1="&#x21a;" u2="&#x131;" k="52" />
    <hkern u1="&#x21a;" u2="&#x12d;" k="-32" />
    <hkern u1="&#x21a;" u2="&#x12b;" k="-24" />
    <hkern u1="&#x21a;" u2="&#x129;" k="-48" />
    <hkern u1="&#x21a;" u2="&#x11f;" k="75" />
    <hkern u1="&#x21a;" u2="&#x109;" k="43" />
    <hkern u1="&#x21a;" u2="&#xf0;" k="18" />
    <hkern u1="&#x21a;" u2="&#xef;" k="-58" />
    <hkern u1="&#x21a;" u2="&#xee;" k="-55" />
    <hkern u1="&#x21a;" u2="&#xec;" k="-68" />
    <hkern u1="&#x21a;" u2="&#xe4;" k="54" />
    <hkern u1="&#x21a;" u2="&#xe3;" k="44" />
    <hkern u1="&#x21a;" u2="&#xc6;" k="49" />
    <hkern u1="&#x21a;" u2="x" k="49" />
    <hkern u1="&#x21a;" u2="v" k="49" />
    <hkern u1="&#x21a;" u2="f" k="15" />
    <hkern u1="&#x21a;" u2="&#x40;" k="15" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="49" />
    <hkern u1="&#x21a;" u2="&#x26;" k="10" />
    <hkern u1="&#x21b;" u2="\" k="17" />
    <hkern u1="&#x21b;" u2="V" k="6" />
    <hkern u1="&#x1e80;" u2="&#x135;" k="-24" />
    <hkern u1="&#x1e80;" u2="&#x131;" k="13" />
    <hkern u1="&#x1e80;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e80;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e80;" u2="&#x129;" k="-39" />
    <hkern u1="&#x1e80;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-53" />
    <hkern u1="&#x1e80;" u2="&#xee;" k="-30" />
    <hkern u1="&#x1e80;" u2="&#xec;" k="-54" />
    <hkern u1="&#x1e80;" u2="&#xc6;" k="23" />
    <hkern u1="&#x1e80;" u2="&#x2f;" k="29" />
    <hkern u1="&#x1e81;" u2="&#xf0;" k="7" />
    <hkern u1="&#x1e81;" u2="&#xc6;" k="14" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="15" />
    <hkern u1="&#x1e81;" u2="]" k="21" />
    <hkern u1="&#x1e81;" u2="\" k="18" />
    <hkern u1="&#x1e81;" u2="X" k="23" />
    <hkern u1="&#x1e81;" u2="V" k="9" />
    <hkern u1="&#x1e81;" u2="&#x2f;" k="14" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="14" />
    <hkern u1="&#x1e82;" u2="&#x135;" k="-24" />
    <hkern u1="&#x1e82;" u2="&#x131;" k="13" />
    <hkern u1="&#x1e82;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e82;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e82;" u2="&#x129;" k="-39" />
    <hkern u1="&#x1e82;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-53" />
    <hkern u1="&#x1e82;" u2="&#xee;" k="-30" />
    <hkern u1="&#x1e82;" u2="&#xec;" k="-54" />
    <hkern u1="&#x1e82;" u2="&#xc6;" k="23" />
    <hkern u1="&#x1e82;" u2="&#x2f;" k="29" />
    <hkern u1="&#x1e83;" u2="&#xf0;" k="7" />
    <hkern u1="&#x1e83;" u2="&#xc6;" k="14" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="15" />
    <hkern u1="&#x1e83;" u2="]" k="21" />
    <hkern u1="&#x1e83;" u2="\" k="18" />
    <hkern u1="&#x1e83;" u2="X" k="23" />
    <hkern u1="&#x1e83;" u2="V" k="9" />
    <hkern u1="&#x1e83;" u2="&#x2f;" k="14" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="14" />
    <hkern u1="&#x1e84;" u2="&#x135;" k="-24" />
    <hkern u1="&#x1e84;" u2="&#x131;" k="13" />
    <hkern u1="&#x1e84;" u2="&#x12d;" k="-26" />
    <hkern u1="&#x1e84;" u2="&#x12b;" k="-18" />
    <hkern u1="&#x1e84;" u2="&#x129;" k="-39" />
    <hkern u1="&#x1e84;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-53" />
    <hkern u1="&#x1e84;" u2="&#xee;" k="-30" />
    <hkern u1="&#x1e84;" u2="&#xec;" k="-54" />
    <hkern u1="&#x1e84;" u2="&#xc6;" k="23" />
    <hkern u1="&#x1e84;" u2="&#x2f;" k="29" />
    <hkern u1="&#x1e85;" u2="&#xf0;" k="7" />
    <hkern u1="&#x1e85;" u2="&#xc6;" k="14" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="15" />
    <hkern u1="&#x1e85;" u2="]" k="21" />
    <hkern u1="&#x1e85;" u2="\" k="18" />
    <hkern u1="&#x1e85;" u2="X" k="23" />
    <hkern u1="&#x1e85;" u2="V" k="9" />
    <hkern u1="&#x1e85;" u2="&#x2f;" k="14" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="14" />
    <hkern u1="&#x1eab;" u2="&#x2122;" k="15" />
    <hkern u1="&#x1eab;" u2="v" k="6" />
    <hkern u1="&#x1eab;" u2="\" k="43" />
    <hkern u1="&#x1eab;" u2="V" k="28" />
    <hkern u1="&#x1eab;" u2="&#x3f;" k="13" />
    <hkern u1="&#x1eab;" u2="&#x2a;" k="8" />
    <hkern u1="&#x1eb0;" u2="&#x2122;" k="30" />
    <hkern u1="&#x1eb0;" u2="&#xf0;" k="5" />
    <hkern u1="&#x1eb0;" u2="&#xae;" k="18" />
    <hkern u1="&#x1eb0;" u2="v" k="14" />
    <hkern u1="&#x1eb0;" u2="f" k="8" />
    <hkern u1="&#x1eb0;" u2="\" k="42" />
    <hkern u1="&#x1eb0;" u2="V" k="27" />
    <hkern u1="&#x1eb0;" u2="&#x3f;" k="14" />
    <hkern u1="&#x1eb0;" u2="&#x2a;" k="27" />
    <hkern u1="&#x1ec5;" u2="&#x2122;" k="12" />
    <hkern u1="&#x1ec5;" u2="&#xc6;" k="5" />
    <hkern u1="&#x1ec5;" u2="&#x7d;" k="10" />
    <hkern u1="&#x1ec5;" u2="v" k="6" />
    <hkern u1="&#x1ec5;" u2="\" k="38" />
    <hkern u1="&#x1ec5;" u2="X" k="5" />
    <hkern u1="&#x1ec5;" u2="V" k="25" />
    <hkern u1="&#x1ec5;" u2="&#x3f;" k="11" />
    <hkern u1="&#x1ec5;" u2="&#x29;" k="12" />
    <hkern u1="&#x1ed7;" u2="&#x2122;" k="14" />
    <hkern u1="&#x1ed7;" u2="&#xc6;" k="7" />
    <hkern u1="&#x1ed7;" u2="&#x7d;" k="19" />
    <hkern u1="&#x1ed7;" u2="x" k="10" />
    <hkern u1="&#x1ed7;" u2="v" k="7" />
    <hkern u1="&#x1ed7;" u2="]" k="27" />
    <hkern u1="&#x1ed7;" u2="\" k="41" />
    <hkern u1="&#x1ed7;" u2="X" k="26" />
    <hkern u1="&#x1ed7;" u2="V" k="29" />
    <hkern u1="&#x1ed7;" u2="&#x3f;" k="15" />
    <hkern u1="&#x1ed7;" u2="&#x2a;" k="8" />
    <hkern u1="&#x1ed7;" u2="&#x29;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x1ef9;" k="24" />
    <hkern u1="&#x1ef2;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#x1ef2;" u2="&#x159;" k="26" />
    <hkern u1="&#x1ef2;" u2="&#x155;" k="32" />
    <hkern u1="&#x1ef2;" u2="&#x151;" k="48" />
    <hkern u1="&#x1ef2;" u2="&#x142;" k="6" />
    <hkern u1="&#x1ef2;" u2="&#x135;" k="-15" />
    <hkern u1="&#x1ef2;" u2="&#x131;" k="59" />
    <hkern u1="&#x1ef2;" u2="&#x12d;" k="-44" />
    <hkern u1="&#x1ef2;" u2="&#x12b;" k="-36" />
    <hkern u1="&#x1ef2;" u2="&#x129;" k="-49" />
    <hkern u1="&#x1ef2;" u2="&#x103;" k="56" />
    <hkern u1="&#x1ef2;" u2="&#xff;" k="24" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="26" />
    <hkern u1="&#x1ef2;" u2="&#xef;" k="-70" />
    <hkern u1="&#x1ef2;" u2="&#xee;" k="-21" />
    <hkern u1="&#x1ef2;" u2="&#xec;" k="-62" />
    <hkern u1="&#x1ef2;" u2="&#xeb;" k="61" />
    <hkern u1="&#x1ef2;" u2="&#xe4;" k="43" />
    <hkern u1="&#x1ef2;" u2="&#xe3;" k="34" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="14" />
    <hkern u1="&#x1ef2;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1ef2;" u2="&#xae;" k="19" />
    <hkern u1="&#x1ef2;" u2="x" k="34" />
    <hkern u1="&#x1ef2;" u2="v" k="33" />
    <hkern u1="&#x1ef2;" u2="f" k="23" />
    <hkern u1="&#x1ef2;" u2="&#x40;" k="35" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="65" />
    <hkern u1="&#x1ef2;" u2="&#x2a;" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="28" />
    <hkern u1="&#x1ef3;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1ef3;" u2="&#xc6;" k="18" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="11" />
    <hkern u1="&#x1ef3;" u2="]" k="19" />
    <hkern u1="&#x1ef3;" u2="\" k="18" />
    <hkern u1="&#x1ef3;" u2="X" k="22" />
    <hkern u1="&#x1ef3;" u2="V" k="6" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="19" />
    <hkern u1="&#x1ef8;" u2="&#x1ef9;" k="24" />
    <hkern u1="&#x1ef8;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#x1ef8;" u2="&#x159;" k="26" />
    <hkern u1="&#x1ef8;" u2="&#x155;" k="32" />
    <hkern u1="&#x1ef8;" u2="&#x151;" k="48" />
    <hkern u1="&#x1ef8;" u2="&#x142;" k="6" />
    <hkern u1="&#x1ef8;" u2="&#x135;" k="-15" />
    <hkern u1="&#x1ef8;" u2="&#x131;" k="59" />
    <hkern u1="&#x1ef8;" u2="&#x12d;" k="-44" />
    <hkern u1="&#x1ef8;" u2="&#x12b;" k="-36" />
    <hkern u1="&#x1ef8;" u2="&#x129;" k="-49" />
    <hkern u1="&#x1ef8;" u2="&#x103;" k="56" />
    <hkern u1="&#x1ef8;" u2="&#xff;" k="24" />
    <hkern u1="&#x1ef8;" u2="&#xf0;" k="26" />
    <hkern u1="&#x1ef8;" u2="&#xef;" k="-70" />
    <hkern u1="&#x1ef8;" u2="&#xee;" k="-21" />
    <hkern u1="&#x1ef8;" u2="&#xec;" k="-62" />
    <hkern u1="&#x1ef8;" u2="&#xeb;" k="61" />
    <hkern u1="&#x1ef8;" u2="&#xe4;" k="43" />
    <hkern u1="&#x1ef8;" u2="&#xe3;" k="34" />
    <hkern u1="&#x1ef8;" u2="&#xdf;" k="14" />
    <hkern u1="&#x1ef8;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1ef8;" u2="&#xae;" k="19" />
    <hkern u1="&#x1ef8;" u2="x" k="34" />
    <hkern u1="&#x1ef8;" u2="v" k="33" />
    <hkern u1="&#x1ef8;" u2="f" k="23" />
    <hkern u1="&#x1ef8;" u2="&#x40;" k="35" />
    <hkern u1="&#x1ef8;" u2="&#x2f;" k="65" />
    <hkern u1="&#x1ef8;" u2="&#x2a;" k="-5" />
    <hkern u1="&#x1ef8;" u2="&#x26;" k="28" />
    <hkern u1="&#x1ef9;" u2="&#xf0;" k="13" />
    <hkern u1="&#x1ef9;" u2="&#xc6;" k="18" />
    <hkern u1="&#x1ef9;" u2="&#x7d;" k="11" />
    <hkern u1="&#x1ef9;" u2="]" k="19" />
    <hkern u1="&#x1ef9;" u2="\" k="18" />
    <hkern u1="&#x1ef9;" u2="X" k="22" />
    <hkern u1="&#x1ef9;" u2="V" k="6" />
    <hkern u1="&#x1ef9;" u2="&#x2f;" k="19" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="9" />
    <hkern u1="&#x2013;" u2="x" k="23" />
    <hkern u1="&#x2013;" u2="v" k="9" />
    <hkern u1="&#x2013;" u2="f" k="11" />
    <hkern u1="&#x2013;" u2="X" k="32" />
    <hkern u1="&#x2013;" u2="V" k="24" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="9" />
    <hkern u1="&#x2014;" u2="x" k="23" />
    <hkern u1="&#x2014;" u2="v" k="9" />
    <hkern u1="&#x2014;" u2="f" k="11" />
    <hkern u1="&#x2014;" u2="X" k="32" />
    <hkern u1="&#x2014;" u2="V" k="24" />
    <hkern u1="&#x2018;" u2="&#x135;" k="-8" />
    <hkern u1="&#x2018;" u2="&#x12d;" k="-11" />
    <hkern u1="&#x2018;" u2="&#x129;" k="-24" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-36" />
    <hkern u1="&#x2018;" u2="&#xee;" k="-15" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-39" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="42" />
    <hkern u1="&#x2019;" u2="&#x12d;" k="-20" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="-12" />
    <hkern u1="&#x2019;" u2="&#x129;" k="-30" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-46" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-9" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-43" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="46" />
    <hkern u1="&#x2019;" u2="&#x40;" k="24" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="73" />
    <hkern u1="&#x2019;" u2="&#x26;" k="23" />
    <hkern u1="&#x201a;" u2="v" k="23" />
    <hkern u1="&#x201a;" u2="f" k="11" />
    <hkern u1="&#x201a;" u2="V" k="40" />
    <hkern u1="&#x201c;" u2="&#x135;" k="-8" />
    <hkern u1="&#x201c;" u2="&#x12d;" k="-11" />
    <hkern u1="&#x201c;" u2="&#x129;" k="-24" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-36" />
    <hkern u1="&#x201c;" u2="&#xee;" k="-15" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-39" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="42" />
    <hkern u1="&#x201d;" u2="&#x135;" k="-7" />
    <hkern u1="&#x201d;" u2="&#x12d;" k="-20" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="-12" />
    <hkern u1="&#x201d;" u2="&#x129;" k="-30" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-46" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-9" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-43" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="46" />
    <hkern u1="&#x201d;" u2="&#x40;" k="24" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="73" />
    <hkern u1="&#x201d;" u2="&#x26;" k="23" />
    <hkern u1="&#x201e;" u2="v" k="23" />
    <hkern u1="&#x201e;" u2="f" k="11" />
    <hkern u1="&#x201e;" u2="V" k="40" />
    <hkern u1="&#x2039;" u2="V" k="11" />
    <hkern u1="&#x203a;" u2="&#x141;" k="-7" />
    <hkern u1="&#x203a;" u2="x" k="21" />
    <hkern u1="&#x203a;" u2="f" k="10" />
    <hkern u1="&#x203a;" u2="X" k="20" />
    <hkern u1="&#x203a;" u2="V" k="23" />
    <hkern u1="&#x2122;" u2="&#x1eb0;" k="22" />
    <hkern u1="&#x2122;" u2="&#x1fc;" k="22" />
    <hkern u1="&#x2122;" u2="&#x1fa;" k="22" />
    <hkern u1="&#x2122;" u2="&#x135;" k="-25" />
    <hkern u1="&#x2122;" u2="&#x134;" k="11" />
    <hkern u1="&#x2122;" u2="&#x12d;" k="-16" />
    <hkern u1="&#x2122;" u2="&#x129;" k="-17" />
    <hkern u1="&#x2122;" u2="&#x104;" k="22" />
    <hkern u1="&#x2122;" u2="&#x102;" k="22" />
    <hkern u1="&#x2122;" u2="&#x100;" k="22" />
    <hkern u1="&#x2122;" u2="&#xef;" k="-35" />
    <hkern u1="&#x2122;" u2="&#xee;" k="-32" />
    <hkern u1="&#x2122;" u2="&#xec;" k="-45" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="28" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="22" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="22" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="22" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="22" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="22" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="22" />
    <hkern u1="&#x2122;" u2="J" k="11" />
    <hkern u1="&#x2122;" u2="A" k="22" />
    <hkern g1="questiondown.cap" u2="&#x1ef8;" k="23" />
    <hkern g1="questiondown.cap" u2="&#x1ef2;" k="23" />
    <hkern g1="questiondown.cap" u2="&#x21a;" k="10" />
    <hkern g1="questiondown.cap" u2="&#x178;" k="23" />
    <hkern g1="questiondown.cap" u2="&#x176;" k="23" />
    <hkern g1="questiondown.cap" u2="&#x166;" k="10" />
    <hkern g1="questiondown.cap" u2="&#x164;" k="10" />
    <hkern g1="questiondown.cap" u2="&#xdd;" k="23" />
    <hkern g1="questiondown.cap" u2="Y" k="23" />
    <hkern g1="questiondown.cap" u2="V" k="15" />
    <hkern g1="questiondown.cap" u2="T" k="10" />
    <hkern g1="endash.cap" u2="&#xc6;" k="12" />
    <hkern g1="endash.cap" u2="X" k="38" />
    <hkern g1="endash.cap" u2="V" k="21" />
    <hkern g1="emdash.cap" u2="&#xc6;" k="12" />
    <hkern g1="emdash.cap" u2="X" k="38" />
    <hkern g1="emdash.cap" u2="V" k="21" />
    <hkern g1="parenleft.cap" u2="&#x1fe;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x152;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x150;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x14e;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x14c;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x134;" k="-23" />
    <hkern g1="parenleft.cap" u2="&#x128;" k="-30" />
    <hkern g1="parenleft.cap" u2="&#x122;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x120;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x11e;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x11c;" k="15" />
    <hkern g1="parenleft.cap" u2="&#x10c;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x10a;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x108;" k="14" />
    <hkern g1="parenleft.cap" u2="&#x106;" k="14" />
    <hkern g1="parenleft.cap" u2="&#xd8;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xd6;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xd5;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xd4;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xd3;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xd2;" k="15" />
    <hkern g1="parenleft.cap" u2="&#xcf;" k="-31" />
    <hkern g1="parenleft.cap" u2="&#xce;" k="-46" />
    <hkern g1="parenleft.cap" u2="&#xc7;" k="14" />
    <hkern g1="parenleft.cap" u2="Q" k="15" />
    <hkern g1="parenleft.cap" u2="O" k="15" />
    <hkern g1="parenleft.cap" u2="G" k="15" />
    <hkern g1="parenleft.cap" u2="C" k="14" />
    <hkern g1="bracketleft.cap" u2="&#x1fe;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x152;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x150;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x14e;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x14c;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x134;" k="-19" />
    <hkern g1="bracketleft.cap" u2="&#x128;" k="-20" />
    <hkern g1="bracketleft.cap" u2="&#x122;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x120;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x11e;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x11c;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#x10c;" k="17" />
    <hkern g1="bracketleft.cap" u2="&#x10a;" k="17" />
    <hkern g1="bracketleft.cap" u2="&#x108;" k="17" />
    <hkern g1="bracketleft.cap" u2="&#x106;" k="17" />
    <hkern g1="bracketleft.cap" u2="&#xd8;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xd6;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xd5;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xd4;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xd3;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xd2;" k="20" />
    <hkern g1="bracketleft.cap" u2="&#xcf;" k="-22" />
    <hkern g1="bracketleft.cap" u2="&#xce;" k="-41" />
    <hkern g1="bracketleft.cap" u2="&#xc7;" k="17" />
    <hkern g1="bracketleft.cap" u2="Q" k="20" />
    <hkern g1="bracketleft.cap" u2="O" k="20" />
    <hkern g1="bracketleft.cap" u2="G" k="20" />
    <hkern g1="bracketleft.cap" u2="C" k="17" />
    <hkern g1="braceleft.cap" u2="&#x1fe;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x152;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x150;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x14e;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x14c;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x134;" k="-18" />
    <hkern g1="braceleft.cap" u2="&#x12a;" k="-20" />
    <hkern g1="braceleft.cap" u2="&#x128;" k="-23" />
    <hkern g1="braceleft.cap" u2="&#x122;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x120;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x11e;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x11c;" k="14" />
    <hkern g1="braceleft.cap" u2="&#x10c;" k="13" />
    <hkern g1="braceleft.cap" u2="&#x10a;" k="13" />
    <hkern g1="braceleft.cap" u2="&#x108;" k="13" />
    <hkern g1="braceleft.cap" u2="&#x106;" k="13" />
    <hkern g1="braceleft.cap" u2="&#xd8;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xd6;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xd5;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xd4;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xd3;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xd2;" k="14" />
    <hkern g1="braceleft.cap" u2="&#xcf;" k="-21" />
    <hkern g1="braceleft.cap" u2="&#xce;" k="-40" />
    <hkern g1="braceleft.cap" u2="&#xc7;" k="13" />
    <hkern g1="braceleft.cap" u2="Q" k="14" />
    <hkern g1="braceleft.cap" u2="O" k="14" />
    <hkern g1="braceleft.cap" u2="G" k="14" />
    <hkern g1="braceleft.cap" u2="C" k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="endash.cap,emdash.cap"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quoteright,quotedblright"
	k="26" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="t,tcaron,tbar,tcommaaccent"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,uni1EB0"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="d,q,dcaron,dcroat"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="endash.cap,emdash.cap"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="18" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="26" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="endash.cap,emdash.cap"
	k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="hyphen,endash,emdash"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="5" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="6" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="J,Jcircumflex"
	g2="d,q,dcaron,dcroat"
	k="5" />
    <hkern g1="J,Jcircumflex"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="J,Jcircumflex"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="J,Jcircumflex"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="14" />
    <hkern g1="K,Kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="endash.cap,emdash.cap"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="6" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="76" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="44" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="85" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="endash.cap,emdash.cap"
	k="61" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="76" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="75" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="76" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="t,tcaron,tbar,tcommaaccent"
	k="11" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="18" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="34" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="48" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="7" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="22" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="d,q,dcaron,dcroat"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="15" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="8" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="6" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="67" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="75" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="endash.cap,emdash.cap"
	k="57" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="69" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="t,tcaron,tbar,tcommaaccent"
	k="10" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="50" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="hyphen,endash,emdash"
	k="57" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="54" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="37" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="72" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="55" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="52" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="colon,semicolon"
	k="48" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="guillemotright,guilsinglright"
	k="48" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="56" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="69" />
    <hkern g1="T,Tcaron,Tbar,Tcommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="d,q,dcaron,dcroat"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="9" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="d,q,dcaron,dcroat"
	k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="21" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="endash.cap,emdash.cap"
	k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="28" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="d,q,dcaron,dcroat"
	k="67" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="endash.cap,emdash.cap"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="t,tcaron,tbar,tcommaaccent"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="hyphen,endash,emdash"
	k="64" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="J,Jcircumflex"
	k="19" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="guillemotleft,guilsinglleft"
	k="58" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="colon,semicolon"
	k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="guillemotright,guilsinglright"
	k="36" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	g2="z,zacute,zdotaccent,zcaron"
	k="49" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="d,q,dcaron,dcroat"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="endash.cap,emdash.cap"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="19" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="69" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="9" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="66" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,uni1EAB"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="7" />
    <hkern g1="b,p,thorn"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="68" />
    <hkern g1="b,p,thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="b,p,thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="67" />
    <hkern g1="b,p,thorn"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="b,p,thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="8" />
    <hkern g1="b,p,thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="9" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="74" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="48" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="17" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="colon,semicolon"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="48" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="37" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="73" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="79" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EC5"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="50" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="32" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="51" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="38" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="55" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="56" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quoteright,quotedblright"
	k="19" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedbl,quotesingle"
	k="24" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zacute,zdotaccent,zcaron"
	k="12" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="18" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="57" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="64" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteright,quotedblright"
	k="55" />
    <hkern g1="hyphen,endash,emdash"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="hyphen,endash,emdash"
	g2="t,tcaron,tbar,tcommaaccent"
	k="9" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="endash.cap,emdash.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="15" />
    <hkern g1="endash.cap,emdash.cap"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="57" />
    <hkern g1="endash.cap,emdash.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="endash.cap,emdash.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="58" />
    <hkern g1="endash.cap,emdash.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="9" />
    <hkern g1="endash.cap,emdash.cap"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="endash.cap,emdash.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="25" />
    <hkern g1="k,kcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="54" />
    <hkern g1="k,kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="36" />
    <hkern g1="k,kcommaaccent"
	g2="d,q,dcaron,dcroat"
	k="7" />
    <hkern g1="k,kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="7" />
    <hkern g1="k,kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="9" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="23" />
    <hkern g1="k,kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="d,q,dcaron,dcroat"
	k="12" />
    <hkern g1="dcaron,lcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="dcaron,lcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="14" />
    <hkern g1="dcaron,lcaron"
	g2="quotedbl,quotesingle"
	k="-25" />
    <hkern g1="dcaron,lcaron"
	g2="t,tcaron,tbar,tcommaaccent"
	k="-22" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-13" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="-21" />
    <hkern g1="dcaron,lcaron"
	g2="hyphen,endash,emdash"
	k="54" />
    <hkern g1="dcaron,lcaron"
	g2="guillemotleft,guilsinglleft"
	k="44" />
    <hkern g1="dcaron,lcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="53" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="4" />
    <hkern g1="dcaron,lcaron"
	g2="b,thorn"
	k="-51" />
    <hkern g1="dcaron,lcaron"
	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent"
	k="-51" />
    <hkern g1="dcaron,lcaron"
	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex"
	k="-51" />
    <hkern g1="dcaron,lcaron"
	g2="l,lacute,lcommaaccent,lcaron,lslash"
	k="-45" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="71" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="7" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="67" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="5" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="7" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="71" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="69" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="9" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="6" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,uni1ED7"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="115" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteright,quotedblright"
	k="119" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="125" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="t,tcaron,tbar,tcommaaccent"
	k="14" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="25" />
    <hkern g1="quoteleft,quotedblleft"
	g2="d,q,dcaron,dcroat"
	k="17" />
    <hkern g1="quoteleft,quotedblleft"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="13" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="33" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="129" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="d,q,dcaron,dcroat"
	k="25" />
    <hkern g1="quoteright,quotedblright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="20" />
    <hkern g1="quoteright,quotedblright"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="23" />
    <hkern g1="quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="75" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="37" />
    <hkern g1="quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="13" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotleft,guilsinglleft"
	k="46" />
    <hkern g1="quoteright,quotedblright"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="8" />
    <hkern g1="quoteright,quotedblright"
	g2="colon,semicolon"
	k="12" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotright,guilsinglright"
	k="13" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="139" />
    <hkern g1="quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="17" />
    <hkern g1="quotedbl,quotesingle"
	g2="d,q,dcaron,dcroat"
	k="13" />
    <hkern g1="quotedbl,quotesingle"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="11" />
    <hkern g1="quotedbl,quotesingle"
	g2="hyphen,endash,emdash"
	k="61" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="125" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="43" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="26" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="J,Jcircumflex"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="51" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="11" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="51" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	k="7" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="47" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="33" />
    <hkern g1="t,tbar,tcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="53" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="59" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="17" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="50" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="34" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="d,q,dcaron,dcroat"
	k="8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute,uni1EC5,uni1ED7"
	k="8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EB0"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EAB"
	k="8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="guillemotleft,guilsinglleft"
	k="11" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="26" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave,uni1EF9"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcaron,Tbar,Tcommaaccent"
	k="71" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,uni1EF8"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="12" />
  </font>
</defs></svg>
